#!/usr/bin/env python3
"""
Ultimate Co-founder Production Server
Serves the production-built frontend and backend in a production-ready configuration
"""

import uvicorn
import os
import subprocess
import signal
import sys
import time
import threading
from pathlib import Path
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProductionServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_built = False
        self.production_app = None
        
    def check_build_exists(self):
        """Check if production build exists"""
        dist_path = Path("dist")
        if dist_path.exists() and (dist_path / "index.html").exists():
            logger.info("✅ Production build found")
            return True
        else:
            logger.warning("❌ Production build not found")
            return False
    
    def create_production_build(self):
        """Create production build if it doesn't exist"""
        if not self.check_build_exists():
            logger.info("🔨 Creating production build...")
            try:
                result = subprocess.run(["npm", "run", "build"], 
                                      capture_output=True, text=True, timeout=120)
                if result.returncode == 0:
                    logger.info("✅ Production build created successfully")
                    self.frontend_built = True
                    return True
                else:
                    logger.error(f"❌ Build failed: {result.stderr}")
                    return False
            except subprocess.TimeoutExpired:
                logger.error("❌ Build timed out")
                return False
            except Exception as e:
                logger.error(f"❌ Build error: {e}")
                return False
        else:
            self.frontend_built = True
            return True
    
    def create_production_app(self):
        """Create FastAPI app that serves both backend API and frontend static files"""
        # Import the main backend app
        sys.path.append(str(Path("backend").absolute()))
        from main import app as backend_app
        
        # Create production app
        production_app = FastAPI(
            title="Ultimate Co-founder - Production",
            description="Production deployment of Ultimate Co-founder with integrated frontend and backend",
            version="1.0.0"
        )
        
        # Mount backend API
        production_app.mount("/api", backend_app)
        production_app.mount("/docs", backend_app)
        production_app.mount("/redoc", backend_app)
        production_app.mount("/openapi.json", backend_app)
        
        # Mount static files
        if Path("dist").exists():
            production_app.mount("/assets", StaticFiles(directory="dist/assets"), name="assets")
            
            # Serve index.html for all non-API routes (SPA routing)
            @production_app.get("/{full_path:path}")
            async def serve_spa(full_path: str):
                # If it's an API route, let it pass through
                if full_path.startswith("api/") or full_path.startswith("docs") or full_path.startswith("redoc"):
                    return {"error": "Not found"}
                
                # For all other routes, serve index.html (SPA routing)
                return FileResponse("dist/index.html")
            
            @production_app.get("/")
            async def serve_root():
                return FileResponse("dist/index.html")
        
        self.production_app = production_app
        return production_app
    
    def start_production_server(self, host="0.0.0.0", port=8080):
        """Start the production server"""
        logger.info("🚀 Starting Ultimate Co-founder Production Server")
        logger.info("=" * 60)
        
        # Create build
        if not self.create_production_build():
            logger.error("❌ Failed to create production build")
            return False
        
        # Create production app
        try:
            app = self.create_production_app()
            logger.info("✅ Production app created successfully")
        except Exception as e:
            logger.error(f"❌ Failed to create production app: {e}")
            return False
        
        # Start server
        logger.info(f"🌐 Starting server on http://{host}:{port}")
        logger.info("📊 Available endpoints:")
        logger.info(f"   • Frontend: http://{host}:{port}/")
        logger.info(f"   • API Docs: http://{host}:{port}/docs")
        logger.info(f"   • Backend API: http://{host}:{port}/api/")
        logger.info("=" * 60)
        
        try:
            uvicorn.run(
                app,
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )
        except KeyboardInterrupt:
            logger.info("🛑 Server stopped by user")
        except Exception as e:
            logger.error(f"❌ Server error: {e}")
            return False
        
        return True
    
    def health_check(self, host="localhost", port=8080):
        """Perform health check on the production server"""
        import requests
        import time
        
        logger.info("🔍 Performing production server health check...")
        
        # Wait for server to start
        time.sleep(3)
        
        try:
            # Test frontend
            frontend_response = requests.get(f"http://{host}:{port}/", timeout=10)
            frontend_ok = frontend_response.status_code == 200
            
            # Test API
            api_response = requests.get(f"http://{host}:{port}/api/v1/health/", timeout=10)
            api_ok = api_response.status_code == 200
            
            # Test API docs
            docs_response = requests.get(f"http://{host}:{port}/docs", timeout=10)
            docs_ok = docs_response.status_code == 200
            
            logger.info("📊 Health Check Results:")
            logger.info(f"   • Frontend: {'✅ OK' if frontend_ok else '❌ FAIL'}")
            logger.info(f"   • Backend API: {'✅ OK' if api_ok else '❌ FAIL'}")
            logger.info(f"   • API Docs: {'✅ OK' if docs_ok else '❌ FAIL'}")
            
            if frontend_ok and api_ok and docs_ok:
                logger.info("🎉 Production server is fully operational!")
                return True
            else:
                logger.warning("⚠️ Some services are not responding correctly")
                return False
                
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return False

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    logger.info("🛑 Received shutdown signal")
    sys.exit(0)

def main():
    """Main entry point"""
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start production server
    server = ProductionServer()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Ultimate Co-founder Production Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8080, help="Port to bind to")
    parser.add_argument("--health-check", action="store_true", help="Run health check only")
    
    args = parser.parse_args()
    
    if args.health_check:
        # Run health check in a separate thread
        def run_health_check():
            time.sleep(5)  # Wait for server to start
            server.health_check(args.host, args.port)
        
        health_thread = threading.Thread(target=run_health_check)
        health_thread.daemon = True
        health_thread.start()
    
    # Start the production server
    success = server.start_production_server(args.host, args.port)
    
    if not success:
        logger.error("❌ Failed to start production server")
        sys.exit(1)

if __name__ == "__main__":
    main()
