import React from 'react';
import { createRoot } from 'react-dom/client';

const SimpleApp: React.FC = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🚀 Simple React App Test</h1>
      <p>If you can see this, React is working!</p>
      <button onClick={() => alert('Button clicked!')}>
        Test Button
      </button>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0' }}>
        <h2>Authentication Test</h2>
        <p>This is a simple test to verify React rendering.</p>
        <ul>
          <li>✅ React is rendering</li>
          <li>✅ JavaScript is executing</li>
          <li>✅ Event handlers work</li>
        </ul>
      </div>
    </div>
  );
};

// Mount the app
const rootElement = document.getElementById('root');
if (rootElement) {
  const root = createRoot(rootElement);
  root.render(<SimpleApp />);
} else {
  console.error('Root element not found!');
}
