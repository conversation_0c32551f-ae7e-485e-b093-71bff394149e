#!/usr/bin/env python3
"""
Frontend Authentication Test
Test the complete authentication flow including frontend and backend integration
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx
from datetime import datetime

# Test configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5173"

class FrontendAuthTester:
    def __init__(self):
        self.backend_client = httpx.AsyncClient(timeout=30.0)
        self.frontend_client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
        
    async def test_backend_auth_endpoints(self) -> bool:
        """Test backend authentication endpoints"""
        try:
            print("🧪 Testing Backend Authentication Endpoints...")
            
            # Test registration
            test_user = {
                "email": f"test_{int(time.time())}@example.com",
                "password": "testpass123",
                "name": "Test User"
            }
            
            response = await self.backend_client.post(
                f"{BACKEND_URL}/api/v1/auth/register",
                json=test_user
            )
            
            if response.status_code != 200:
                print(f"❌ Backend registration failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            print("✅ Backend registration working")
            
            # Test login
            login_data = {
                "username": test_user["email"],
                "password": test_user["password"]
            }
            
            response = await self.backend_client.post(
                f"{BACKEND_URL}/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                print(f"❌ Backend login failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            token_data = response.json()
            access_token = token_data.get("access_token")
            
            if not access_token:
                print("❌ No access token received")
                return False
            
            print("✅ Backend login working")
            
            # Test /me endpoint
            response = await self.backend_client.get(
                f"{BACKEND_URL}/api/v1/auth/me",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            
            if response.status_code != 200:
                print(f"❌ Backend /me endpoint failed: {response.status_code}")
                return False
            
            user_data = response.json()
            print(f"✅ Backend /me endpoint working - User: {user_data.get('email')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Backend auth test error: {e}")
            return False
    
    async def test_frontend_accessibility(self) -> bool:
        """Test frontend page accessibility"""
        try:
            print("🧪 Testing Frontend Accessibility...")
            
            # Test main page
            response = await self.frontend_client.get(FRONTEND_URL)
            if response.status_code != 200:
                print(f"❌ Frontend main page failed: {response.status_code}")
                return False
            
            print("✅ Frontend main page accessible")
            
            # Test auth page
            response = await self.frontend_client.get(f"{FRONTEND_URL}/auth")
            if response.status_code != 200:
                print(f"❌ Frontend auth page failed: {response.status_code}")
                return False
            
            print("✅ Frontend auth page accessible")
            
            return True
            
        except Exception as e:
            print(f"❌ Frontend accessibility test error: {e}")
            return False
    
    async def test_api_configuration(self) -> bool:
        """Test API configuration and CORS"""
        try:
            print("🧪 Testing API Configuration...")
            
            # Test CORS preflight
            response = await self.frontend_client.options(
                f"{BACKEND_URL}/api/v1/auth/login",
                headers={
                    "Origin": FRONTEND_URL,
                    "Access-Control-Request-Method": "POST",
                    "Access-Control-Request-Headers": "Content-Type"
                }
            )
            
            print(f"CORS preflight response: {response.status_code}")
            print(f"CORS headers: {dict(response.headers)}")
            
            # Test health endpoint from frontend perspective
            response = await self.frontend_client.get(
                f"{BACKEND_URL}/health",
                headers={"Origin": FRONTEND_URL}
            )
            
            if response.status_code != 200:
                print(f"❌ Health endpoint failed: {response.status_code}")
                return False
            
            print("✅ API configuration working")
            return True
            
        except Exception as e:
            print(f"❌ API configuration test error: {e}")
            return False
    
    async def test_ai_api_fallbacks(self) -> bool:
        """Test AI API fallbacks when OpenAI key is not available"""
        try:
            print("🧪 Testing AI API Fallbacks...")
            
            # First authenticate to get token
            test_user = {
                "username": "<EMAIL>",
                "password": "demo123"
            }
            
            response = await self.backend_client.post(
                f"{BACKEND_URL}/api/v1/auth/login",
                data=test_user,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code != 200:
                print(f"❌ Login for AI test failed: {response.status_code}")
                return False
            
            token_data = response.json()
            access_token = token_data.get("access_token")
            auth_headers = {"Authorization": f"Bearer {access_token}"}
            
            # Test agent execution (should use free APIs)
            response = await self.backend_client.post(
                f"{BACKEND_URL}/api/v1/agents/execute?agent_id=strategic",
                json={"description": "Test strategic analysis for AI fallback"},
                headers=auth_headers
            )
            
            if response.status_code != 200:
                print(f"❌ Agent execution failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
            
            result = response.json()
            print(f"✅ AI agent execution working - Response length: {len(str(result))}")
            
            # Test SkyworkAI integration
            response = await self.backend_client.post(
                f"{BACKEND_URL}/api/v1/skywork/generate",
                json={
                    "type": "business_plan",
                    "title": "Test Business Plan",
                    "prompt": "Create a test business plan for AI fallback testing"
                },
                headers=auth_headers
            )
            
            if response.status_code != 200:
                print(f"❌ SkyworkAI integration failed: {response.status_code}")
                return False
            
            print("✅ SkyworkAI integration working")
            
            return True
            
        except Exception as e:
            print(f"❌ AI API fallback test error: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive authentication and functionality test"""
        print("🚀 Starting Comprehensive Frontend Authentication Test")
        print("=" * 70)
        
        tests = [
            ("Backend Authentication Endpoints", self.test_backend_auth_endpoints),
            ("Frontend Accessibility", self.test_frontend_accessibility),
            ("API Configuration & CORS", self.test_api_configuration),
            ("AI API Fallbacks", self.test_ai_api_fallbacks),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    self.test_results.append({"test": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
                self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 70)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL AUTHENTICATION TESTS PASSED!")
            print("🚀 Frontend and Backend Authentication is WORKING!")
            return True
        else:
            print("⚠️  Some authentication tests FAILED!")
            return False
    
    async def close(self):
        """Clean up resources"""
        await self.backend_client.aclose()
        await self.frontend_client.aclose()

async def main():
    """Main test runner"""
    tester = FrontendAuthTester()
    try:
        success = await tester.run_comprehensive_test()
        return 0 if success else 1
    finally:
        await tester.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
