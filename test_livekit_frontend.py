#!/usr/bin/env python3
"""
Test script to verify LiveKit functionality in the Ultimate Co-founder application.
This script tests the complete LiveKit session flow from frontend to backend.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
FRONTEND_URL = "http://localhost:5173"
BACKEND_URL = "http://localhost:8000"

def test_livekit_session_creation():
    """Test LiveKit session creation through the API"""
    print("🎥 Testing LiveKit Session Creation")
    print("=" * 50)
    
    try:
        # Test session creation
        session_data = {
            "agent_ids": ["strategic", "product", "technical"],
            "session_type": "video"
        }
        
        print(f"📡 Creating session with data: {session_data}")
        
        response = requests.post(
            f"{BACKEND_URL}/api/v1/livekit/sessions",
            json=session_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Session created successfully!")
            print(f"   Session ID: {session['id']}")
            print(f"   Room Name: {session['room_name']}")
            print(f"   Session Type: {session['session_type']}")
            print(f"   Participants: {len(session['participants'])}")
            print(f"   Status: {session['status']}")
            
            # Test session retrieval
            print(f"\n🔍 Testing session retrieval...")
            get_response = requests.get(f"{BACKEND_URL}/api/v1/livekit/sessions/{session['id']}")
            
            if get_response.status_code == 200:
                retrieved_session = get_response.json()
                print(f"✅ Session retrieved successfully!")
                print(f"   Retrieved ID: {retrieved_session['id']}")
            else:
                print(f"❌ Failed to retrieve session: {get_response.status_code}")
            
            # Test session start
            print(f"\n▶️ Testing session start...")
            start_response = requests.post(f"{BACKEND_URL}/api/v1/livekit/sessions/{session['id']}/start")
            
            if start_response.status_code == 200:
                print(f"✅ Session started successfully!")
            else:
                print(f"❌ Failed to start session: {start_response.status_code}")
            
            # Test session end
            print(f"\n⏹️ Testing session end...")
            end_response = requests.post(f"{BACKEND_URL}/api/v1/livekit/sessions/{session['id']}/end")
            
            if end_response.status_code == 200:
                print(f"✅ Session ended successfully!")
            else:
                print(f"❌ Failed to end session: {end_response.status_code}")
            
            return True
            
        else:
            print(f"❌ Failed to create session: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing LiveKit session: {e}")
        return False

def test_livekit_service_status():
    """Test LiveKit service status"""
    print("\n🔧 Testing LiveKit Service Status")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/livekit/")
        
        if response.status_code == 200:
            status = response.json()
            print(f"✅ LiveKit service is active!")
            print(f"   Status: {status['status']}")
            print(f"   Active Sessions: {status['active_sessions']}")
            return True
        else:
            print(f"❌ LiveKit service check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking LiveKit service: {e}")
        return False

def test_frontend_api_connectivity():
    """Test frontend API connectivity through Vite proxy"""
    print("\n🌐 Testing Frontend API Connectivity")
    print("=" * 50)
    
    try:
        # Test through Vite proxy
        response = requests.get(f"{FRONTEND_URL}/api/v1/livekit/")
        
        if response.status_code == 200:
            print(f"✅ Frontend can reach LiveKit API through Vite proxy!")
            return True
        else:
            print(f"❌ Frontend API connectivity failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing frontend connectivity: {e}")
        return False

def main():
    """Run all LiveKit tests"""
    print("🚀 Ultimate Co-founder LiveKit Test Suite")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("LiveKit Service Status", test_livekit_service_status),
        ("Frontend API Connectivity", test_frontend_api_connectivity),
        ("LiveKit Session Creation", test_livekit_session_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nTotal Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED - LiveKit is ready for production!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed - Check the issues above")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
