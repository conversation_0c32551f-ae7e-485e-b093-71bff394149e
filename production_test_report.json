{"summary": {"total_tests": 9, "passed": 8, "failed": 1, "success_rate": 88.88888888888889, "duration": 2.949537, "timestamp": "2025-06-30T16:25:15.365873"}, "tests": [{"test": "Backend Health Check", "status": "PASS", "details": "Status: healthy", "duration": "0.39s", "timestamp": "2025-06-30T16:25:12.802734"}, {"test": "API Documentation", "status": "PASS", "details": "Swagger UI accessible", "duration": "0.01s", "timestamp": "2025-06-30T16:25:12.815626"}, {"test": "AI Agents Endpoint", "status": "PASS", "details": "Found 1 agents", "duration": "0.01s", "timestamp": "2025-06-30T16:25:12.830294"}, {"test": "AI Agent Execution", "status": "PASS", "details": "Strategic agent responded with 141 chars", "duration": "1.65s", "timestamp": "2025-06-30T16:25:14.485408"}, {"test": "Multi-Agent Execution", "status": "PASS", "details": "Collaboration completed with 2 agents", "duration": "0.75s", "timestamp": "2025-06-30T16:25:15.237447"}, {"test": "SkyworkAI Integration", "status": "PASS", "details": "Document generated: SaaS Startup Business Plan", "duration": "0.01s", "timestamp": "2025-06-30T16:25:15.246262"}, {"test": "TopView.ai Integration", "status": "FAIL", "details": "HTTP 401", "duration": "0.00s", "timestamp": "2025-06-30T16:25:15.250936"}, {"test": "Frontend Production Build", "status": "PASS", "details": "All required build files present", "duration": "0.00s", "timestamp": "2025-06-30T16:25:15.251017"}, {"test": "Frontend Accessibility", "status": "PASS", "details": "HTML structure valid", "duration": "0.11s", "timestamp": "2025-06-30T16:25:15.365792"}]}