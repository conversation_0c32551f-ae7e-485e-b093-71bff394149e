#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset the demo user for testing the authentication system.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from sqlalchemy.orm import Session
from backend.app.core.database import SessionLocal, engine
from backend.app.models.user import User
from backend.app.api.routes.auth import get_password_hash
import uuid

def reset_demo_user():
    """Reset the demo user for testing purposes."""
    db = SessionLocal()
    
    try:
        # Delete existing demo user if exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
            print("Deleted existing demo user")
        
        # Create fresh demo user
        demo_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            name="Demo User",
            hashed_password=get_password_hash("demo123"),
            is_active=True
        )
        
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        
        print("✅ Demo user created successfully!")
        print(f"Email: {demo_user.email}")
        print(f"Name: {demo_user.name}")
        print(f"ID: {demo_user.id}")
        print(f"Password: demo123")
        
    except Exception as e:
        print(f"❌ Error creating demo user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    reset_demo_user()
