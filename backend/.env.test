# Test Environment Configuration for System Testing

# OpenAI Configuration (Mock for testing)
OPENAI_API_KEY=test-key-mock-for-system-testing

# LiveKit Configuration (Mock for testing)
LIVEKIT_API_KEY=test-livekit-key
LIVEKIT_API_SECRET=test-livekit-secret
LIVEKIT_URL=wss://test-livekit-server.livekit.cloud

# Composio Configuration (Mock for testing)
COMPOSIO_API_KEY=test-composio-key

# Database Configuration (SQLite for testing)
DATABASE_URL=sqlite:///./test_ultimate_cofounder.db
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=test-secret-key-for-system-testing-only
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
FRONTEND_URL=http://localhost:5173

# Bolt.new Integration (Mock for testing)
BOLT_API_KEY=test-bolt-api-key

# Testing Mode
TESTING=true
