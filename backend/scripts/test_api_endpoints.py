#!/usr/bin/env python3
"""
Comprehensive API endpoint testing script
"""
import asyncio
import aiohttp
import json
import logging
from typing import Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = None
        self.test_results = []

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def test_endpoint(self, method: str, endpoint: str, data: Dict[str, Any] = None, expected_status: int = 200) -> Dict[str, Any]:
        """Test a single API endpoint"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    status = response.status
                    content = await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    status = response.status
                    content = await response.json()
            else:
                raise ValueError(f"Unsupported method: {method}")

            success = status == expected_status
            result = {
                "endpoint": endpoint,
                "method": method,
                "status": status,
                "expected_status": expected_status,
                "success": success,
                "response": content if success else None,
                "error": None if success else f"Status {status}, expected {expected_status}"
            }
            
            if success:
                logger.info(f"✅ {method} {endpoint} - Status: {status}")
            else:
                logger.error(f"❌ {method} {endpoint} - Status: {status}, Expected: {expected_status}")
                logger.error(f"   Response: {content}")
            
            self.test_results.append(result)
            return result

        except Exception as e:
            logger.error(f"❌ {method} {endpoint} - Exception: {e}")
            result = {
                "endpoint": endpoint,
                "method": method,
                "status": None,
                "expected_status": expected_status,
                "success": False,
                "response": None,
                "error": str(e)
            }
            self.test_results.append(result)
            return result

    async def run_all_tests(self):
        """Run comprehensive API tests"""
        logger.info("🚀 Starting comprehensive API endpoint testing")
        logger.info("=" * 60)

        # Test basic endpoints
        await self.test_endpoint("GET", "/")
        await self.test_endpoint("GET", "/health")

        # Test agent endpoints
        await self.test_endpoint("GET", "/api/v1/agents")
        await self.test_endpoint("GET", "/api/v1/agents/strategic")
        await self.test_endpoint("GET", "/api/v1/agents/product")
        await self.test_endpoint("GET", "/api/v1/agents/technical")
        await self.test_endpoint("GET", "/api/v1/agents/operations")
        await self.test_endpoint("GET", "/api/v1/agents/marketing")

        # Test orchestrator endpoint
        orchestrator_data = {
            "description": "I want to create a SaaS platform for booking micro-events like pop-up dinners and workshops",
            "agent_ids": ["strategic", "product"],
            "include_livekit": False
        }
        await self.test_endpoint("POST", "/api/v1/orchestrator/process", orchestrator_data)

        # Test health check endpoint
        health_data = {
            "test_idea": "TestAppX – a SaaS for booking micro-events."
        }
        await self.test_endpoint("POST", "/api/v1/health/check", health_data)

        # Test integrations endpoints
        await self.test_endpoint("GET", "/api/v1/integrations")

        # Test LiveKit endpoints
        livekit_data = {
            "agent_ids": ["strategic", "product"],
            "session_type": "video"
        }
        await self.test_endpoint("POST", "/api/v1/livekit/sessions", livekit_data)

        # Test auth endpoints
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "name": "Test User"
        }
        await self.test_endpoint("POST", "/api/v1/auth/register", user_data)

        # Generate summary
        self.generate_summary()

    def generate_summary(self):
        """Generate test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 API ENDPOINT TEST SUMMARY")
        logger.info("=" * 60)

        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - successful_tests

        logger.info(f"📈 Total Tests: {total_tests}")
        logger.info(f"✅ Successful: {successful_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"📊 Success Rate: {(successful_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            logger.info("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["success"]:
                    logger.info(f"   {result['method']} {result['endpoint']} - {result['error']}")

        logger.info("\n✅ SUCCESSFUL TESTS:")
        for result in self.test_results:
            if result["success"]:
                logger.info(f"   {result['method']} {result['endpoint']} - Status: {result['status']}")

        return {
            "total": total_tests,
            "successful": successful_tests,
            "failed": failed_tests,
            "success_rate": (successful_tests/total_tests)*100
        }

async def main():
    async with APITester() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
