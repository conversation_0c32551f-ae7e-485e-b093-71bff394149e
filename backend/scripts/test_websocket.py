#!/usr/bin/env python3
"""
WebSocket functionality testing script
"""
import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

WEBSOCKET_URL = "ws://localhost:8000/ws/test-client-123"

async def test_websocket_connection():
    """Test WebSocket connection and messaging"""
    logger.info("🔌 Testing WebSocket connection...")
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            logger.info("✅ WebSocket connection established")
            
            # Test sending a message
            test_message = {
                "type": "test",
                "data": "Hello WebSocket!"
            }
            
            await websocket.send(json.dumps(test_message))
            logger.info("📤 Sent test message")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📥 Received response: {response}")
                return True
            except asyncio.TimeoutError:
                logger.info("⏰ No response received (timeout)")
                return True  # Connection works even if no response
                
    except Exception as e:
        logger.error(f"❌ WebSocket connection failed: {e}")
        return False

async def test_websocket_echo():
    """Test WebSocket echo functionality"""
    logger.info("🔄 Testing WebSocket echo functionality...")

    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            logger.info("✅ WebSocket connection established for echo test")

            # Test echo message
            test_message = "Hello Ultimate Co-founder!"

            await websocket.send(test_message)
            logger.info(f"📤 Sent echo message: {test_message}")

            # Wait for echo response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📥 Received echo response: {response}")

                # Check if it's an echo
                if "Echo:" in response:
                    logger.info("✅ Echo functionality working correctly")
                    return True
                else:
                    logger.info("ℹ️ Received response but not echo format")
                    return True

            except asyncio.TimeoutError:
                logger.info("⏰ No echo response received (timeout)")
                return False

    except Exception as e:
        logger.error(f"❌ WebSocket echo test failed: {e}")
        return False

async def main():
    """Run all WebSocket tests"""
    logger.info("🚀 Starting WebSocket functionality tests")
    logger.info("=" * 50)
    
    results = []
    
    # Test basic WebSocket connection
    basic_result = await test_websocket_connection()
    results.append(("Basic WebSocket", basic_result))

    # Test WebSocket echo functionality
    echo_result = await test_websocket_echo()
    results.append(("WebSocket Echo", echo_result))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 WEBSOCKET TEST SUMMARY")
    logger.info("=" * 50)
    
    total_tests = len(results)
    successful_tests = sum(1 for _, success in results if success)
    
    logger.info(f"📈 Total Tests: {total_tests}")
    logger.info(f"✅ Successful: {successful_tests}")
    logger.info(f"❌ Failed: {total_tests - successful_tests}")
    logger.info(f"📊 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        logger.info(f"   {status} {test_name}")

if __name__ == "__main__":
    asyncio.run(main())
