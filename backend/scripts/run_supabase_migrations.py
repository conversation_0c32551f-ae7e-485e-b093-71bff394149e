#!/usr/bin/env python3
"""
Script to run Supabase migrations
"""
import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.config import settings
from app.services.supabase_client import supabase_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def run_migrations():
    """Run all Supabase migrations"""
    
    if not supabase_service.is_connected():
        logger.error("❌ Supabase client not connected. Please check your configuration.")
        return False
    
    migrations_dir = backend_dir / "supabase_migrations"
    if not migrations_dir.exists():
        logger.error(f"❌ Migrations directory not found: {migrations_dir}")
        return False
    
    # Get all SQL migration files
    migration_files = sorted(migrations_dir.glob("*.sql"))
    
    if not migration_files:
        logger.warning("⚠️ No migration files found")
        return True
    
    client = supabase_service.get_client()
    
    for migration_file in migration_files:
        logger.info(f"🔄 Running migration: {migration_file.name}")
        
        try:
            # Read the migration file
            with open(migration_file, 'r') as f:
                sql_content = f.read()
            
            # Execute the migration using Supabase's RPC function
            # Note: This requires a custom function in Supabase to execute raw SQL
            # For now, we'll log the SQL and provide instructions
            logger.info(f"📝 Migration content for {migration_file.name}:")
            logger.info("=" * 50)
            logger.info(sql_content)
            logger.info("=" * 50)
            
            logger.info(f"✅ Migration {migration_file.name} prepared")
            
        except Exception as e:
            logger.error(f"❌ Failed to process migration {migration_file.name}: {e}")
            return False
    
    logger.info("🎉 All migrations processed successfully!")
    logger.info("📋 Please execute the SQL statements above in your Supabase SQL editor")
    return True

async def test_database_connection():
    """Test the database connection and basic operations"""
    
    if not supabase_service.is_connected():
        logger.error("❌ Supabase client not connected")
        return False
    
    client = supabase_service.get_client()
    
    try:
        # Test basic connection by querying agents table
        result = client.table('agents').select('*').limit(5).execute()
        
        if result.data:
            logger.info(f"✅ Database connection successful. Found {len(result.data)} agents.")
            for agent in result.data:
                logger.info(f"  - {agent.get('name', 'Unknown')} ({agent.get('role', 'Unknown role')})")
        else:
            logger.info("✅ Database connection successful. No agents found yet.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False

async def main():
    """Main function"""
    logger.info("🚀 Starting Supabase migration runner")
    
    # Check configuration
    if not settings.SUPABASE_URL or not settings.SUPABASE_ANON_KEY:
        logger.error("❌ Supabase configuration missing. Please set SUPABASE_URL and SUPABASE_ANON_KEY")
        return
    
    logger.info(f"🔗 Connecting to Supabase: {settings.SUPABASE_URL}")
    
    # Test connection first
    connection_ok = await test_database_connection()
    if not connection_ok:
        logger.error("❌ Database connection failed. Please check your Supabase configuration.")
        return
    
    # Run migrations
    success = await run_migrations()
    
    if success:
        logger.info("🎉 Migration process completed successfully!")
    else:
        logger.error("❌ Migration process failed!")

if __name__ == "__main__":
    asyncio.run(main())
