["tests/test_api_endpoints.py::TestAgentEndpoints::test_execute_single_agent_task", "tests/test_api_endpoints.py::TestAgentEndpoints::test_get_all_agents", "tests/test_api_endpoints.py::TestAgentEndpoints::test_get_nonexistent_agent", "tests/test_api_endpoints.py::TestAgentEndpoints::test_get_specific_agent", "tests/test_api_endpoints.py::TestAuthEndpoints::test_register_duplicate_user", "tests/test_api_endpoints.py::TestAuthEndpoints::test_register_user", "tests/test_api_endpoints.py::TestHealthEndpoints::test_health_endpoint", "tests/test_api_endpoints.py::TestHealthEndpoints::test_root_endpoint", "tests/test_api_endpoints.py::TestIntegrationEndpoints::test_get_integrations", "tests/test_api_endpoints.py::TestLiveKitEndpoints::test_create_session", "tests/test_api_endpoints.py::TestOrchestratorEndpoints::test_get_suggestions", "tests/test_api_endpoints.py::TestOrchestratorEndpoints::test_process_task", "tests/test_models.py::TestAgentModel::test_create_agent", "tests/test_models.py::TestModelIntegration::test_complete_workflow", "tests/test_models.py::TestSessionModel::test_create_session", "tests/test_models.py::TestSessionModel::test_session_user_relationship", "tests/test_models.py::TestTaskModel::test_create_task", "tests/test_models.py::TestTaskModel::test_task_relationships", "tests/test_models.py::TestUserModel::test_create_user", "tests/test_models.py::TestUserModel::test_user_unique_email", "tests/test_services.py::TestCrewAIService::test_execute_multi_agent_task", "tests/test_services.py::TestCrewAIService::test_execute_single_agent_task", "tests/test_services.py::TestCrewAIService::test_invalid_agent_id", "tests/test_services.py::TestCrewAIService::test_service_initialization", "tests/test_services.py::TestServiceConfiguration::test_crewai_mock_mode_configuration", "tests/test_services.py::TestServiceConfiguration::test_supabase_fallback_configuration", "tests/test_services.py::TestServiceIntegration::test_crewai_with_database_context", "tests/test_services.py::TestServiceIntegration::test_service_error_handling", "tests/test_services.py::TestSupabaseService::test_connection_test_without_credentials", "tests/test_services.py::TestSupabaseService::test_get_client", "tests/test_services.py::TestSupabaseService::test_service_initialization"]