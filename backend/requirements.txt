# Core AI Framework
crewai==0.28.8
langchain>=0.1.10,<0.2.0
langchain-openai>=0.1.8
langchain-community>=0.0.38

# API Framework
fastapi==0.110.2
uvicorn[standard]==0.29.0
python-multipart==0.0.9
websockets==12.0

# Integrations
composio-core==0.3.7
livekit==1.0.11
livekit-api==1.0.3
supabase==2.4.2

# Database & Storage
sqlalchemy==2.0.30
alembic==1.13.1
redis==5.0.4

# Utilities
python-dotenv==1.0.0
pydantic==2.7.1
httpx==0.27.0
aiofiles>=24.0.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Development
pytest==8.2.0
pytest-asyncio==0.23.6
black==24.4.2
isort==5.13.2