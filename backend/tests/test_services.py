"""
Service layer tests
"""
import pytest
from unittest.mock import Mock, patch
from app.services.crewai_service import crewai_service
from app.services.supabase_client import supabase_service

class TestCrewAIService:
    """Test CrewAI service functionality"""
    
    def test_service_initialization(self):
        """Test CrewAI service initializes correctly"""
        assert crewai_service is not None
        assert hasattr(crewai_service, 'mock_mode')
        # In test environment, should be in mock mode
        assert crewai_service.mock_mode is True
    
    @pytest.mark.asyncio
    async def test_execute_single_agent_task(self):
        """Test single agent task execution"""
        result = await crewai_service.execute_single_agent_task(
            agent_id="strategic",
            description="Test task description",
            context="Test context"
        )
        
        assert "task_id" in result
        assert "result" in result
        assert "metadata" in result
        assert result["metadata"]["mock_mode"] is True
        assert "strategic" in result["result"]
    
    @pytest.mark.asyncio
    async def test_execute_multi_agent_task(self):
        """Test multi-agent task execution"""
        results = await crewai_service.execute_multi_agent_task(
            description="Test multi-agent task",
            agent_ids=["strategic", "product"]
        )
        
        assert isinstance(results, list)
        assert len(results) == 2
        
        for result in results:
            assert "task_id" in result
            assert "agent_id" in result
            assert "result" in result
            assert "metadata" in result
            assert result["metadata"]["mock_mode"] is True
    
    @pytest.mark.asyncio
    async def test_invalid_agent_id(self):
        """Test handling of invalid agent ID"""
        # In mock mode, invalid agent IDs are handled gracefully
        result = await crewai_service.execute_single_agent_task(
            agent_id="nonexistent",
            description="Test task",
            context=""
        )
        assert result is not None
        assert "mock" in result.get("metadata", {}).get("model", "").lower()

class TestSupabaseService:
    """Test Supabase service functionality"""
    
    def test_service_initialization(self):
        """Test Supabase service initializes correctly"""
        assert supabase_service is not None
        assert hasattr(supabase_service, 'client')
    
    @pytest.mark.asyncio
    async def test_connection_test_without_credentials(self):
        """Test connection test without credentials"""
        # Should handle missing credentials gracefully
        result = await supabase_service.test_connection()
        # In test environment without credentials, should return False
        assert isinstance(result, bool)
    
    def test_get_client(self):
        """Test getting Supabase client"""
        client = supabase_service.get_client()
        # Should return None or client instance
        assert client is None or hasattr(client, 'table')

class TestServiceIntegration:
    """Test service integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_crewai_with_database_context(self, db_session):
        """Test CrewAI service with database context"""
        # This tests that services work with database session
        result = await crewai_service.execute_single_agent_task(
            agent_id="strategic",
            description="Database integration test",
            context="Testing with database session"
        )
        
        assert result is not None
        assert "task_id" in result
    
    @pytest.mark.asyncio
    async def test_service_error_handling(self):
        """Test service error handling"""
        # Test that services handle errors gracefully
        try:
            # This should not crash the application
            await supabase_service.test_connection()
            crewai_service.mock_mode  # Access property
        except Exception as e:
            pytest.fail(f"Service error handling failed: {e}")

class TestServiceConfiguration:
    """Test service configuration"""
    
    def test_crewai_mock_mode_configuration(self):
        """Test CrewAI mock mode configuration"""
        # In test environment, should be in mock mode
        assert crewai_service.mock_mode is True
        
        # Mock mode should provide consistent responses
        assert hasattr(crewai_service, 'execute_single_agent_task')
        assert hasattr(crewai_service, 'execute_multi_agent_task')
    
    def test_supabase_fallback_configuration(self):
        """Test Supabase fallback configuration"""
        # Should handle missing configuration gracefully
        assert supabase_service is not None
        
        # Should not crash when credentials are missing
        try:
            supabase_service.get_client()
        except Exception as e:
            pytest.fail(f"Supabase fallback failed: {e}")
