"""
API endpoint tests
"""
import pytest
from fastapi.testclient import TestClient

class TestHealthEndpoints:
    """Test health check endpoints"""
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "Ultimate" in data["message"]
    
    def test_health_endpoint(self, client: TestClient):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data

class TestAgentEndpoints:
    """Test agent-related endpoints"""
    
    def test_get_all_agents(self, client: TestClient):
        """Test getting all agents"""
        response = client.get("/api/v1/agents")
        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert isinstance(data["agents"], list)
        assert len(data["agents"]) == 5  # strategic, product, technical, operations, marketing

        # Check agent structure
        agent = data["agents"][0]
        required_fields = ["id", "name", "description", "capabilities"]
        for field in required_fields:
            assert field in agent
    
    def test_get_specific_agent(self, client: TestClient):
        """Test getting specific agent details"""
        agent_ids = ["strategic", "product", "technical", "operations", "marketing"]
        
        for agent_id in agent_ids:
            response = client.get(f"/api/v1/agents/{agent_id}")
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == agent_id
            assert "name" in data
            assert "role" in data
            assert "capabilities" in data
    
    def test_get_nonexistent_agent(self, client: TestClient):
        """Test getting non-existent agent"""
        response = client.get("/api/v1/agents/nonexistent")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    def test_execute_single_agent_task(self, client: TestClient, sample_agent_task):
        """Test executing single agent task"""
        response = client.post("/api/v1/agents/strategic/execute", json=sample_agent_task)
        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert "result" in data
        assert "metadata" in data

class TestOrchestratorEndpoints:
    """Test orchestrator endpoints"""
    
    def test_process_task(self, client: TestClient, sample_task_data):
        """Test processing orchestrator task"""
        response = client.post("/api/v1/orchestrator/process", json=sample_task_data)
        assert response.status_code == 200
        data = response.json()
        assert "responses" in data
        assert isinstance(data["responses"], list)
        assert len(data["responses"]) > 0

        # Check result structure
        result = data["responses"][0]
        assert "task_id" in result
        assert "agent_id" in result
        assert "result" in result
    
    def test_get_suggestions(self, client: TestClient):
        """Test getting orchestrator suggestions"""
        response = client.get("/api/v1/orchestrator/suggestions")
        assert response.status_code == 200
        data = response.json()
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)

class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_register_user(self, client: TestClient, sample_user_data):
        """Test user registration"""
        response = client.post("/api/v1/auth/register", json=sample_user_data)
        assert response.status_code == 200
        data = response.json()
        assert "email" in data
        assert "id" in data
        assert data["email"] == sample_user_data["email"]
        assert data["name"] == sample_user_data["name"]
    
    def test_register_duplicate_user(self, client: TestClient, sample_user_data):
        """Test registering duplicate user"""
        # Register first user
        client.post("/api/v1/auth/register", json=sample_user_data)
        
        # Try to register same user again
        response = client.post("/api/v1/auth/register", json=sample_user_data)
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]

class TestIntegrationEndpoints:
    """Test integration endpoints"""
    
    def test_get_integrations(self, client: TestClient):
        """Test getting available integrations"""
        response = client.get("/api/v1/integrations")
        assert response.status_code == 200
        data = response.json()
        assert "integrations" in data
        assert isinstance(data["integrations"], list)

class TestLiveKitEndpoints:
    """Test LiveKit endpoints"""
    
    def test_create_session(self, client: TestClient):
        """Test creating LiveKit session"""
        session_data = {
            "agent_ids": ["strategic", "product"],
            "session_type": "video"
        }
        response = client.post("/api/v1/livekit/sessions", json=session_data)
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "room_name" in data
