"""
Database model tests
"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from app.models.user import User
from app.models.agent import Agent
from app.models.session import Session
from app.models.task import Task

class TestUserModel:
    """Test User model"""
    
    def test_create_user(self, db_session):
        """Test creating a user"""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password_here"
        )
        db_session.add(user)
        db_session.commit()
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.is_active is True
        assert user.created_at is not None
    
    def test_user_unique_email(self, db_session):
        """Test user email uniqueness constraint"""
        user1 = User(
            email="<EMAIL>",
            name="Test User 1",
            hashed_password="password1"
        )
        user2 = User(
            email="<EMAIL>",
            name="Test User 2", 
            hashed_password="password2"
        )
        
        db_session.add(user1)
        db_session.commit()
        
        db_session.add(user2)
        with pytest.raises(IntegrityError):
            db_session.commit()

class TestAgentModel:
    """Test Agent model"""
    
    def test_create_agent(self, db_session):
        """Test creating an agent"""
        agent = Agent(
            name="Strategic Agent",
            role="strategic",
            status="active",
            capabilities=["market_analysis", "strategic_planning"]
        )
        db_session.add(agent)
        db_session.commit()
        
        assert agent.id is not None
        assert agent.name == "Strategic Agent"
        assert agent.role == "strategic"
        assert agent.status == "active"
        assert agent.capabilities == ["market_analysis", "strategic_planning"]
        assert agent.created_at is not None

class TestSessionModel:
    """Test Session model"""
    
    def test_create_session(self, db_session):
        """Test creating a session"""
        # Create a user first
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create session
        session = Session(
            user_id=user.id,
            session_type="video",
            status="active",
            room_name="test-room-123"
        )
        db_session.add(session)
        db_session.commit()
        
        assert session.id is not None
        assert session.user_id == user.id
        assert session.session_type == "video"
        assert session.status == "active"
        assert session.room_name == "test-room-123"
        assert session.created_at is not None
    
    def test_session_user_relationship(self, db_session):
        """Test session-user relationship"""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        session = Session(
            user_id=user.id,
            session_type="audio",
            status="active",
            room_name="test-room"
        )
        db_session.add(session)
        db_session.commit()
        
        # Test relationship
        assert session.user == user
        assert session in user.sessions

class TestTaskModel:
    """Test Task model"""
    
    def test_create_task(self, db_session):
        """Test creating a task"""
        # Create user and session first
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        session = Session(
            user_id=user.id,
            session_type="video",
            status="active",
            room_name="test-room"
        )
        db_session.add(session)
        db_session.commit()
        
        # Create task
        task = Task(
            session_id=session.id,
            agent_id="strategic",
            description="Test task description",
            status="pending",
            task_metadata={"priority": "high", "category": "analysis"}
        )
        db_session.add(task)
        db_session.commit()
        
        assert task.id is not None
        assert task.session_id == session.id
        assert task.agent_id == "strategic"
        assert task.description == "Test task description"
        assert task.status == "pending"
        assert task.task_metadata == {"priority": "high", "category": "analysis"}
        assert task.created_at is not None
    
    def test_task_relationships(self, db_session):
        """Test task relationships"""
        # Create user, session, and task
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        session = Session(
            user_id=user.id,
            session_type="video",
            status="active",
            room_name="test-room"
        )
        db_session.add(session)
        db_session.commit()
        
        task = Task(
            session_id=session.id,
            agent_id="strategic",
            description="Test task",
            status="pending"
        )
        db_session.add(task)
        db_session.commit()
        
        # Test relationships
        assert task.session == session
        assert task in session.tasks

class TestModelIntegration:
    """Test model integration scenarios"""
    
    def test_complete_workflow(self, db_session):
        """Test complete workflow with all models"""
        # Create user
        user = User(
            email="<EMAIL>",
            name="Workflow User",
            hashed_password="password"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create agent
        agent = Agent(
            name="Test Agent",
            role="strategic",
            status="active",
            capabilities=["analysis"]
        )
        db_session.add(agent)
        db_session.commit()
        
        # Create session
        session = Session(
            user_id=user.id,
            session_type="video",
            status="active",
            room_name="workflow-room"
        )
        db_session.add(session)
        db_session.commit()
        
        # Create task
        task = Task(
            session_id=session.id,
            agent_id=agent.role,
            description="Workflow test task",
            status="pending"
        )
        db_session.add(task)
        db_session.commit()
        
        # Verify all relationships work
        assert user.sessions[0] == session
        assert session.tasks[0] == task
        assert task.session == session
        assert task.session.user == user
