from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from pydantic import BaseModel

from app.core.database import get_db
from app.services.crewai_service import crewai_service

router = APIRouter()

class TaskRequest(BaseModel):
    description: str
    context: str = None

class MultiAgentTaskRequest(BaseModel):
    description: str
    agent_ids: List[str] = None

@router.get("/")
async def get_agents():
    """Get all available agents"""
    agents = [
        {
            "id": "strategic",
            "name": "Alex Strategic",
            "role": "Strategic Co-founder",
            "status": "active",
            "description": "Strategic planning, market analysis, and business direction",
            "capabilities": ["Market Research", "Strategic Planning", "Competitive Analysis", "Business Modeling"]
        },
        {
            "id": "product", 
            "name": "Sam Product",
            "role": "Product Co-founder",
            "status": "active",
            "description": "Product strategy, user experience, and feature prioritization",
            "capabilities": ["Product Strategy", "UX Design", "Feature Planning", "User Research"]
        },
        {
            "id": "technical",
            "name": "Taylor Tech", 
            "role": "Technical Co-founder",
            "status": "active",
            "description": "Architecture, development, and technical implementation",
            "capabilities": ["System Architecture", "Code Generation", "Technical Review", "DevOps"]
        },
        {
            "id": "operations",
            "name": "Jordan Ops",
            "role": "Operations Co-founder", 
            "status": "active",
            "description": "Process optimization, resource management, and operations",
            "capabilities": ["Process Design", "Resource Planning", "Quality Assurance", "Automation"]
        },
        {
            "id": "marketing",
            "name": "Morgan Marketing",
            "role": "Marketing Co-founder",
            "status": "active", 
            "description": "Growth strategy, brand building, and customer acquisition",
            "capabilities": ["Growth Strategy", "Content Creation", "Analytics", "Campaign Management"]
        }
    ]
    
    return {"agents": agents}

@router.post("/execute")
async def execute_single_agent_task(
    agent_id: str,
    request: TaskRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with a single agent"""
    valid_agents = ["strategic", "product", "technical", "operations", "marketing"]
    if agent_id not in valid_agents:
        raise HTTPException(status_code=400, detail=f"Agent {agent_id} not found")

    try:
        result = await crewai_service.execute_single_agent_task(
            agent_id=agent_id,
            description=request.description,
            context=request.context
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing task: {str(e)}")

@router.post("/execute-multi")
async def execute_multi_agent_task(
    request: MultiAgentTaskRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with multiple agents"""
    try:
        results = await crewai_service.execute_multi_agent_task(
            description=request.description,
            agent_ids=request.agent_ids or ["strategic", "product", "technical", "operations", "marketing"]
        )
        return {"results": results}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing multi-agent task: {str(e)}")

@router.get("/{agent_id}")
async def get_agent(agent_id: str):
    """Get specific agent details"""
    # Define agent details for both mock and production modes
    agent_details = {
        "strategic": {
            "role": "Strategic Co-founder",
            "goal": "Analyze market opportunities and develop comprehensive business strategies",
            "backstory": "Expert strategist with 15+ years in market analysis and business development"
        },
        "product": {
            "role": "Product Co-founder",
            "goal": "Design user-centric products and validate market assumptions through research",
            "backstory": "Product visionary with background at Google and Airbnb"
        },
        "technical": {
            "role": "Technical Co-founder",
            "goal": "Architect scalable systems and generate production-ready code",
            "backstory": "Full-stack engineer and system architect with experience at Netflix and Stripe"
        },
        "operations": {
            "role": "Operations Co-founder",
            "goal": "Optimize business processes and ensure operational excellence",
            "backstory": "Operations expert who scaled teams from startup to IPO"
        },
        "marketing": {
            "role": "Marketing Co-founder",
            "goal": "Drive growth through strategic marketing and brand building",
            "backstory": "Growth marketing expert who built viral campaigns for unicorn startups"
        }
    }

    if agent_id not in agent_details:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    details = agent_details[agent_id]
    return {
        "id": agent_id,
        "role": details["role"],
        "goal": details["goal"],
        "backstory": details["backstory"],
        "status": "active"
    }

@router.post("/{agent_id}/status")
async def update_agent_status(agent_id: str, status: str):
    """Update agent status"""
    valid_agents = ["strategic", "product", "technical", "operations", "marketing"]
    if agent_id not in valid_agents:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    # In a real implementation, this would update the agent's status in the database
    return {"agent_id": agent_id, "status": status, "updated_at": "2025-06-30T08:30:00Z"}