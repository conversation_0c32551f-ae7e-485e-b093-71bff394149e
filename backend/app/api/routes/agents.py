from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.core.database import get_db
from app.services.crewai_service import crewai_service

router = APIRouter()

class TaskRequest(BaseModel):
    description: str
    context: Optional[str] = None

class MultiAgentTaskRequest(BaseModel):
    description: str
    agent_ids: Optional[List[str]] = None

@router.get("/")
async def get_agents():
    """Get all available agents"""
    agents = [
        {
            "id": "strategic",
            "name": "Alex Strategic",
            "role": "Strategic Co-founder",
            "status": "active",
            "description": "Strategic planning, market analysis, and business direction",
            "capabilities": ["Market Research", "Strategic Planning", "Competitive Analysis", "Business Modeling"]
        },
        {
            "id": "product", 
            "name": "Sam Product",
            "role": "Product Co-founder",
            "status": "active",
            "description": "Product strategy, user experience, and feature prioritization",
            "capabilities": ["Product Strategy", "UX Design", "Feature Planning", "User Research"]
        },
        {
            "id": "technical",
            "name": "Taylor Tech", 
            "role": "Technical Co-founder",
            "status": "active",
            "description": "Architecture, development, and technical implementation",
            "capabilities": ["System Architecture", "Code Generation", "Technical Review", "DevOps"]
        },
        {
            "id": "operations",
            "name": "Jordan Ops",
            "role": "Operations Co-founder", 
            "status": "active",
            "description": "Process optimization, resource management, and operations",
            "capabilities": ["Process Design", "Resource Planning", "Quality Assurance", "Automation"]
        },
        {
            "id": "marketing",
            "name": "Morgan Marketing",
            "role": "Marketing Co-founder",
            "status": "active", 
            "description": "Growth strategy, brand building, and customer acquisition",
            "capabilities": ["Growth Strategy", "Content Creation", "Analytics", "Campaign Management"]
        }
    ]
    
    return {"agents": agents}

@router.post("/execute")
async def execute_single_agent_task(
    agent_id: str,
    request: TaskRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with a single agent"""
    try:
        result = await crewai_service.execute_single_agent_task(
            agent_id=agent_id,
            description=request.description,
            context=request.context or ""
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing task: {str(e)}")

@router.post("/execute-multi")
async def execute_multi_agent_task(
    request: MultiAgentTaskRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with multiple agents"""
    try:
        results = await crewai_service.execute_multi_agent_task(
            description=request.description,
            agent_ids=request.agent_ids or ["strategic", "product", "technical", "operations", "marketing"]
        )
        return {"results": results}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing multi-agent task: {str(e)}")

@router.get("/{agent_id}")
async def get_agent(agent_id: str):
    """Get specific agent details"""
    # Define agent details (works in both mock and real mode)
    agent_details = {
        "strategic": {
            "id": "strategic",
            "name": "Alex Strategic",
            "role": "Strategic Co-founder",
            "status": "active",
            "description": "Strategic planning, market analysis, and business direction",
            "capabilities": ["Market Research", "Strategic Planning", "Competitive Analysis", "Business Modeling"],
            "backstory": "Expert strategist with 15+ years in market analysis, competitive intelligence, and business development."
        },
        "product": {
            "id": "product",
            "name": "Sam Product",
            "role": "Product Co-founder",
            "status": "active",
            "description": "Product strategy, user experience, and feature prioritization",
            "capabilities": ["Product Strategy", "UX Design", "Feature Planning", "User Research"],
            "backstory": "Product visionary who built user-centric products for millions of users."
        },
        "technical": {
            "id": "technical",
            "name": "Taylor Tech",
            "role": "Technical Co-founder",
            "status": "active",
            "description": "Architecture, development, and technical implementation",
            "capabilities": ["System Architecture", "Code Generation", "Technical Review", "DevOps"],
            "backstory": "Full-stack architect who scaled systems from MVP to millions of users."
        },
        "operations": {
            "id": "operations",
            "name": "Jordan Ops",
            "role": "Operations Co-founder",
            "status": "active",
            "description": "Business operations, legal compliance, and financial planning",
            "capabilities": ["Process Optimization", "Legal Compliance", "Financial Planning", "Team Building"],
            "backstory": "Operations expert who scaled companies from startup to IPO."
        },
        "marketing": {
            "id": "marketing",
            "name": "Morgan Marketing",
            "role": "Marketing Co-founder",
            "status": "active",
            "description": "Growth marketing, brand building, and customer acquisition",
            "capabilities": ["Digital Marketing", "Content Strategy", "Customer Acquisition", "Brand Positioning"],
            "backstory": "Growth marketing expert who built viral campaigns for unicorn startups."
        }
    }

    if agent_id not in agent_details:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    agent_info = agent_details[agent_id]
    return agent_info

@router.post("/{agent_id}/execute")
async def execute_agent_task(
    agent_id: str,
    request: TaskRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with a specific agent"""
    try:
        result = await crewai_service.execute_single_agent_task(
            agent_id=agent_id,
            description=request.description,
            context=request.context or ""
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing task: {str(e)}")

@router.post("/{agent_id}/status")
async def update_agent_status(agent_id: str, status: str):
    """Update agent status"""
    if agent_id not in crewai_service.agents:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")

    # In a real implementation, this would update the agent's status in the database
    return {"agent_id": agent_id, "status": status, "updated_at": "2025-06-25T13:30:00Z"}