from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from pydantic import BaseModel

from app.services.health_service import health_service

router = APIRouter()

class HealthCheckRequest(BaseModel):
    test_idea: str = "TestAppX – a SaaS for booking micro-events."

@router.post("/check")
async def run_health_check(request: HealthCheckRequest):
    """Run a comprehensive system health check"""
    try:
        results = await health_service.run_system_health_check(request.test_idea)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/")
async def get_health_status():
    """Get basic health status"""
    return {
        "status": "healthy",
        "timestamp": "2025-06-25T13:30:00Z",
        "components": {
            "backend": "healthy",
            "database": "healthy",
            "ai_services": "healthy",
            "livekit": "healthy",
            "composio": "healthy"
        }
    }