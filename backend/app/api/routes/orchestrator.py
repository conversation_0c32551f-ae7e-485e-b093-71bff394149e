from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime

from app.services.crewai_service import crewai_service
from app.services.livekit_service import livekit_service
from app.services.composio_service import composio_service

router = APIRouter()

class OrchestrationRequest(BaseModel):
    description: str
    agent_ids: Optional[List[str]] = None
    include_livekit: bool = False
    session_type: str = "video"  # voice, video, screen-share

class SuggestionRequest(BaseModel):
    suggestion_id: str
    agent_id: str
    action: str
    parameters: Dict[str, Any] = {}

@router.post("/process")
async def process_user_input(
    request: OrchestrationRequest,
    background_tasks: BackgroundTasks
):
    """Process user input with AI orchestrator"""
    try:
        # Execute multi-agent analysis
        agent_ids = request.agent_ids or ["strategic", "product", "technical", "operations", "marketing"]
        results = await crewai_service.execute_multi_agent_task(
            description=request.description,
            agent_ids=agent_ids
        )
        
        # Generate suggestions based on results
        suggestions = generate_suggestions(request.description, results)
        
        # Create LiveKit session if requested
        livekit_session = None
        if request.include_livekit:
            livekit_session = await livekit_service.create_session(
                agent_ids=agent_ids,
                session_type=request.session_type
            )
        
        return {
            "responses": results,
            "suggestions": suggestions,
            "livekit_session": livekit_session,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing input: {str(e)}")

@router.post("/execute-suggestion")
async def execute_suggestion(request: SuggestionRequest):
    """Execute a suggested action"""
    try:
        # Execute the suggestion based on agent and action
        if request.action == "market_analysis":
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Conduct comprehensive market analysis: {request.parameters.get('focus', 'general market research')}"
            )
        elif request.action == "user_interviews":
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Plan and conduct user interviews: {request.parameters.get('target_users', 'target user segments')}"
            )
        elif request.action == "technical_architecture":
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Design technical architecture: {request.parameters.get('requirements', 'system requirements')}"
            )
        elif request.action == "legal_setup":
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Establish legal and operational foundation: {request.parameters.get('business_type', 'startup business')}"
            )
        elif request.action == "marketing_strategy":
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Develop marketing and growth strategy: {request.parameters.get('target_market', 'target market')}"
            )
        else:
            # Generic action execution
            result = await crewai_service.execute_single_agent_task(
                agent_id=request.agent_id,
                description=f"Execute action: {request.action} with parameters: {request.parameters}"
            )
        
        return {
            "suggestion_id": request.suggestion_id,
            "result": result,
            "executed_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing suggestion: {str(e)}")

def generate_suggestions(input_text: str, results: List[Dict]) -> List[Dict]:
    """Generate actionable suggestions based on agent results"""
    suggestions = []
    
    # Strategic suggestions
    if any(r["agent_id"] == "strategic" for r in results):
        suggestions.append({
            "id": f"strategic_{uuid.uuid4().hex[:8]}",
            "agent_id": "strategic",
            "agent_name": "Strategic",
            "title": "Conduct Market Analysis",
            "description": "Deep dive into TAM/SAM/SOM, competitive landscape, and market opportunities",
            "action": "market_analysis",
            "priority": "high",
            "estimated_time": "15 min",
            "parameters": {"analysis_type": "comprehensive", "include_competitors": True}
        })
    
    # Product suggestions
    if any(r["agent_id"] == "product" for r in results):
        suggestions.append({
            "id": f"product_{uuid.uuid4().hex[:8]}",
            "agent_id": "product",
            "agent_name": "Product",
            "title": "User Interview Sessions",
            "description": "Conduct 3 video interviews with target users to validate assumptions",
            "action": "user_interviews",
            "priority": "high",
            "estimated_time": "20 min",
            "parameters": {"interview_count": 3, "duration": "5min each"}
        })
    
    # Technical suggestions
    if any(r["agent_id"] == "technical" for r in results):
        suggestions.append({
            "id": f"technical_{uuid.uuid4().hex[:8]}",
            "agent_id": "technical",
            "agent_name": "Technical",
            "title": "Design System Architecture",
            "description": "Create technical blueprint and generate MVP code scaffold",
            "action": "technical_architecture",
            "priority": "medium",
            "estimated_time": "25 min",
            "parameters": {"include_scaffold": True, "deploy_to_bolt": True}
        })
    
    # Operations suggestions
    if any(r["agent_id"] == "operations" for r in results):
        suggestions.append({
            "id": f"operations_{uuid.uuid4().hex[:8]}",
            "agent_id": "operations",
            "agent_name": "Operations",
            "title": "Legal & Financial Setup",
            "description": "Establish business structure, compliance, and financial projections",
            "action": "legal_setup",
            "priority": "medium",
            "estimated_time": "18 min",
            "parameters": {"include_financials": True, "legal_compliance": True}
        })
    
    # Marketing suggestions
    if any(r["agent_id"] == "marketing" for r in results):
        suggestions.append({
            "id": f"marketing_{uuid.uuid4().hex[:8]}",
            "agent_id": "marketing",
            "agent_name": "Marketing",
            "title": "Brand & Growth Strategy",
            "description": "Develop positioning, messaging, and go-to-market plan",
            "action": "marketing_strategy",
            "priority": "low",
            "estimated_time": "12 min",
            "parameters": {"include_branding": True, "gtm_strategy": True}
        })
    
    return suggestions

@router.get("/status")
async def get_orchestrator_status():
    """Get orchestrator status"""
    return {
        "status": "active",
        "agents_available": len(crewai_service.agents),
        "active_sessions": len(livekit_service.sessions),
        "integrations_connected": len([i for i in composio_service.integrations.values() if i["status"] == "connected"]),
        "timestamp": datetime.now().isoformat()
    }