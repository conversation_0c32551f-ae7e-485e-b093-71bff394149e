from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.services.supabase_client import supabase_service
import logging

logger = logging.getLogger(__name__)

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

async def init_db():
    """Initialize database tables"""
    # Import all models here to ensure they are registered
    from app.models import agent, session, task

    # Check if using Supabase
    if supabase_service.is_connected():
        logger.info("Using Supabase database")
        # Test Supabase connection
        connection_ok = await supabase_service.test_connection()
        if connection_ok:
            logger.info("✅ Supabase database connection verified")
        else:
            logger.warning("⚠️ Supabase connection test failed, falling back to local database")
            # Create local tables as fallback
            Base.metadata.create_all(bind=engine)
    else:
        logger.info("Using local SQLite database")
        # Create tables for local development
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Local database initialized")

def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()