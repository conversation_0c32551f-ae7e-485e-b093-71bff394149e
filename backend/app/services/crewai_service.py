from crewai import Agent, Task, Crew, Process
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_groq import <PERSON>t<PERSON><PERSON>q
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime

from app.core.config import settings
from app.models.agent import Agent as AgentModel
from app.models.task import Task as TaskModel

class CrewAIService:
    def __init__(self):
        # Initialize LLM with error handling for missing API key
        try:
            # Priority: Groq API (free) > OpenAI API > Mock mode
            if (settings.GROQ_API_KEY and
                settings.GROQ_API_KEY != "your_groq_api_key_here" and
                not settings.GROQ_API_KEY.startswith("test-")):
                # Use Groq API (free and fast)
                self.llm = ChatGroq(
                    model_name="llama-3.1-70b-versatile",
                    temperature=0.7,
                    api_key=settings.GROQ_API_KEY
                )
                self.mock_mode = False
                print("🚀 CrewAI: Running in PRODUCTION MODE with Groq API (Free)")
            elif (settings.OPENAI_API_KEY and
                  settings.OPENAI_API_KEY != "your_openai_api_key_here" and
                  not settings.OPENAI_API_KEY.startswith("test-") and
                  settings.OPENAI_API_KEY != "test-key-mock-for-system-testing"):
                # Fallback to OpenAI API
                self.llm = ChatOpenAI(
                    model_name="gpt-4",
                    temperature=0.7,
                    api_key=settings.OPENAI_API_KEY
                )
                self.mock_mode = False
                print("🤖 CrewAI: Running in PRODUCTION MODE with OpenAI API")
            else:
                # Use mock mode for development/testing
                self.llm = None
                self.mock_mode = True
                print("🧪 CrewAI: Running in MOCK MODE for testing (no valid API keys)")
        except Exception as e:
            print(f"Warning: Failed to initialize LLM: {e}")
            self.llm = None
            self.mock_mode = True

        self.agents = self._initialize_agents()

        # SkyworkAI-inspired enhancements
        self.research_capabilities = self._initialize_research_capabilities()
    
    def _initialize_agents(self) -> Dict[str, Agent]:
        """Initialize CrewAI agents with specialized roles"""

        # If in mock mode, return empty dict to avoid LLM initialization errors
        if self.mock_mode:
            return {}

        strategic_agent = Agent(
            role="Strategic Co-founder",
            goal="Analyze market opportunities and develop comprehensive business strategies",
            backstory="""You are Alex Strategic, an expert strategist with 15+ years in market analysis, 
            competitive intelligence, and business development. Former McKinsey consultant who has helped 
            100+ startups achieve product-market fit. You excel at TAM/SAM/SOM analysis, competitive 
            research, and strategic planning.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        product_agent = Agent(
            role="Product Co-founder",
            goal="Design user-centric products and validate market assumptions through research",
            backstory="""You are Sam Product, a product visionary with background at Google and Airbnb. 
            You've led product teams that built features used by millions. Expert in user research, 
            design thinking, agile development, and feature prioritization using frameworks like RICE.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        technical_agent = Agent(
            role="Technical Co-founder",
            goal="Architect scalable systems and generate production-ready code",
            backstory="""You are Taylor Tech, a full-stack engineer and system architect with experience 
            at Netflix and Stripe. You've built systems handling millions of requests. Expert in cloud 
            architecture, microservices, modern development practices, and code generation.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        operations_agent = Agent(
            role="Operations Co-founder",
            goal="Establish operational excellence and ensure legal compliance",
            backstory="""You are Jordan Ops, an operations expert who scaled companies from startup to IPO. 
            Former COO with expertise in process optimization, legal compliance, financial planning, 
            team building, and operational efficiency.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        marketing_agent = Agent(
            role="Marketing Co-founder",
            goal="Drive growth through strategic marketing and brand building",
            backstory="""You are Morgan Marketing, a growth marketing expert who built viral campaigns 
            for unicorn startups. Former VP Marketing with expertise in digital marketing, content strategy, 
            customer acquisition, and brand positioning.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        return {
            "strategic": strategic_agent,
            "product": product_agent,
            "technical": technical_agent,
            "operations": operations_agent,
            "marketing": marketing_agent
        }

    def _initialize_research_capabilities(self) -> Dict[str, Any]:
        """Initialize SkyworkAI-inspired research capabilities"""
        return {
            "hierarchical_planning": True,
            "deep_research": True,
            "multi_agent_collaboration": True,
            "automated_reasoning": True,
            "context_awareness": True
        }

    async def execute_single_agent_task(self, agent_id: str, description: str, context: Optional[str] = None) -> Dict[str, Any]:
        """Execute a task with a single agent"""
        task_id = str(uuid.uuid4())
        start_time = datetime.now()

        # If in mock mode, return mock response
        if self.mock_mode:
            return {
                "task_id": task_id,
                "agent_id": agent_id,
                "result": f"Mock response from {agent_id} agent for: {description}",
                "metadata": {
                    "execution_time": 1.5,
                    "model": "mock-gpt-4",
                    "status": "completed",
                    "mock_mode": True
                }
            }

        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        task_id = str(uuid.uuid4())
        
        # Create task with context if provided
        task_description = description
        if context:
            task_description = f"{description}\n\nContext: {context}"
        
        task = Task(
            description=task_description,
            agent=agent,
            expected_output="Comprehensive analysis with actionable recommendations and next steps"
        )
        
        # Create crew with single agent
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=True,
            process=Process.sequential
        )
        
        start_time = datetime.now()
        
        try:
            # Execute the task
            result = crew.kickoff()
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "task_id": task_id,
                "agent_id": agent_id,
                "result": str(result),
                "metadata": {
                    "execution_time": execution_time,
                    "model": "gpt-4",
                    "status": "completed"
                }
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return {
                "task_id": task_id,
                "agent_id": agent_id,
                "result": f"Error executing task: {str(e)}",
                "metadata": {
                    "execution_time": execution_time,
                    "model": "gpt-4",
                    "status": "failed",
                    "error": str(e)
                }
            }
    
    async def execute_multi_agent_task(self, description: str, agent_ids: List[str]) -> List[Dict[str, Any]]:
        """Execute a task with multiple agents collaboratively"""
        if not agent_ids:
            agent_ids = ["strategic", "product", "technical", "operations", "marketing"]

        # If in mock mode, return mock responses for all agents
        if self.mock_mode:
            results = []
            for agent_id in agent_ids:
                results.append({
                    "task_id": str(uuid.uuid4()),
                    "agent_id": agent_id,
                    "result": f"Mock multi-agent response from {agent_id} agent for: {description}",
                    "metadata": {
                        "execution_time": 2.0,
                        "model": "mock-gpt-4",
                        "status": "completed",
                        "mock_mode": True
                    }
                })
            return results

        # Validate agent IDs
        invalid_agents = [aid for aid in agent_ids if aid not in self.agents]
        if invalid_agents:
            raise ValueError(f"Invalid agent IDs: {invalid_agents}")
        
        selected_agents = [self.agents[aid] for aid in agent_ids]
        tasks = []
        
        # Create specialized tasks for each agent
        for i, agent_id in enumerate(agent_ids):
            agent = self.agents[agent_id]
            
            # Customize task description based on agent role
            if agent_id == "strategic":
                task_desc = f"Analyze the business idea from a strategic perspective: {description}. Focus on market analysis, competitive landscape, and strategic recommendations."
            elif agent_id == "product":
                task_desc = f"Evaluate the product aspects of: {description}. Focus on user needs, product-market fit, feature prioritization, and user experience."
            elif agent_id == "technical":
                task_desc = f"Assess the technical requirements for: {description}. Focus on architecture, technology stack, scalability, and implementation approach."
            elif agent_id == "operations":
                task_desc = f"Examine the operational aspects of: {description}. Focus on business processes, legal requirements, financial planning, and operational efficiency."
            elif agent_id == "marketing":
                task_desc = f"Develop marketing strategy for: {description}. Focus on positioning, target audience, growth channels, and brand strategy."
            else:
                task_desc = description
            
            task = Task(
                description=task_desc,
                agent=agent,
                expected_output=f"Detailed {agent_id} analysis with specific recommendations and action items"
            )
            tasks.append(task)
        
        # Create crew with all selected agents
        crew = Crew(
            agents=selected_agents,
            tasks=tasks,
            verbose=True,
            process=Process.sequential
        )
        
        start_time = datetime.now()
        
        try:
            # Execute all tasks
            results = crew.kickoff()
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Format results for each agent
            formatted_results = []
            for i, agent_id in enumerate(agent_ids):
                formatted_results.append({
                    "task_id": str(uuid.uuid4()),
                    "agent_id": agent_id,
                    "result": str(results) if isinstance(results, str) else str(results[i] if i < len(results) else results),
                    "metadata": {
                        "execution_time": execution_time / len(agent_ids),
                        "model": "gpt-4",
                        "status": "completed"
                    }
                })
            
            return formatted_results
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Return error for all agents
            error_results = []
            for agent_id in agent_ids:
                error_results.append({
                    "task_id": str(uuid.uuid4()),
                    "agent_id": agent_id,
                    "result": f"Error in collaborative task: {str(e)}",
                    "metadata": {
                        "execution_time": execution_time / len(agent_ids),
                        "model": "gpt-4",
                        "status": "failed",
                        "error": str(e)
                    }
                })
            
            return error_results

# Global instance
crewai_service = CrewAIService()