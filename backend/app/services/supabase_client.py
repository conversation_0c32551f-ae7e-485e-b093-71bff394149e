"""
Supabase client service for database operations
"""
from supabase import create_client, Client
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class SupabaseService:
    """Service for interacting with Supabase database"""
    
    def __init__(self):
        self.client: Client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Supabase client"""
        try:
            if not settings.SUPABASE_URL or not settings.SUPABASE_ANON_KEY:
                logger.warning("Supabase credentials not configured. Using local database.")
                return
            
            self.client = create_client(
                settings.SUPABASE_URL,
                settings.SUPABASE_ANON_KEY
            )
            logger.info("✅ Supabase client initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Supabase client: {e}")
            self.client = None
    
    def is_connected(self) -> bool:
        """Check if Supabase client is connected"""
        return self.client is not None
    
    async def test_connection(self) -> bool:
        """Test Supabase connection"""
        if not self.client:
            return False
        
        try:
            # Try a simple query to test connection
            result = self.client.table('agents').select('id').limit(1).execute()
            logger.info("✅ Supabase connection test successful")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase connection test failed: {e}")
            return False
    
    def get_client(self) -> Client:
        """Get Supabase client instance"""
        return self.client

# Global instance
supabase_service = SupabaseService()

def get_supabase_client() -> Client:
    """Dependency to get Supabase client"""
    return supabase_service.get_client()
