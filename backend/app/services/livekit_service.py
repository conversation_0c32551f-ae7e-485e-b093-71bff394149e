from livekit import api
from livekit.api import AccessToken, VideoGrant
from typing import Dict, List, Any
import uuid
from datetime import datetime, timedelta
import asyncio

from app.core.config import settings

class LiveKitService:
    def __init__(self):
        self.api_key = settings.LIVEKIT_API_KEY
        self.api_secret = settings.LIVEKIT_API_SECRET
        self.url = settings.LIVEKIT_URL
        self.sessions: Dict[str, Dict] = {}
    
    async def create_session(self, agent_ids: List[str], session_type: str = "video") -> Dict[str, Any]:
        """Create a new LiveKit session"""
        session_id = f"session_{uuid.uuid4().hex[:8]}"
        room_name = f"cofounder_{session_id}"
        
        # Generate token for the user
        token = self._generate_token(room_name, "user", {"canPublish": True, "canSubscribe": True})
        
        # Create session data
        session_data = {
            "id": session_id,
            "room_name": room_name,
            "token": token,
            "url": self.url,
            "status": "pending",
            "session_type": session_type,
            "participants": [
                {
                    "id": "user",
                    "name": "You",
                    "role": "user",
                    "is_local": True,
                    "audio_enabled": True,
                    "video_enabled": session_type == "video",
                    "screen_share_enabled": session_type == "screen-share"
                }
            ],
            "duration": 0,
            "created_at": datetime.now().isoformat(),
            "agent_ids": agent_ids
        }
        
        # Add AI agent participants
        for agent_id in agent_ids:
            agent_name = self._get_agent_name(agent_id)
            session_data["participants"].append({
                "id": agent_id,
                "name": agent_name,
                "role": "agent",
                "is_local": False,
                "audio_enabled": True,
                "video_enabled": session_type == "video",
                "screen_share_enabled": False
            })
        
        self.sessions[session_id] = session_data
        
        # Simulate room creation (in real implementation, create actual LiveKit room)
        await self._create_room(room_name)
        
        return session_data
    
    def _generate_token(self, room_name: str, participant_name: str, grants: Dict = None) -> str:
        """Generate LiveKit access token"""
        if not self.api_key or not self.api_secret:
            # Return mock token for development
            return f"mock_token_{uuid.uuid4().hex[:16]}"
        
        token = AccessToken(self.api_key, self.api_secret)
        token.with_identity(participant_name)
        
        # Set video grants
        video_grant = VideoGrant(
            room_join=True,
            room=room_name,
            can_publish=grants.get("canPublish", True) if grants else True,
            can_subscribe=grants.get("canSubscribe", True) if grants else True,
        )
        token.with_grants(video_grant)
        
        # Token expires in 1 hour
        token.with_ttl(timedelta(hours=1))
        
        return token.to_jwt()
    
    async def _create_room(self, room_name: str):
        """Create LiveKit room"""
        if not self.api_key or not self.api_secret:
            # Mock room creation for development
            await asyncio.sleep(0.1)
            return
        
        try:
            # In real implementation, create actual LiveKit room
            # room_service = api.RoomService(self.url, self.api_key, self.api_secret)
            # await room_service.create_room(api.CreateRoomRequest(name=room_name))
            pass
        except Exception as e:
            print(f"Error creating room: {e}")
    
    def _get_agent_name(self, agent_id: str) -> str:
        """Get agent display name"""
        agent_names = {
            "strategic": "Alex Strategic",
            "product": "Sam Product", 
            "technical": "Taylor Tech",
            "operations": "Jordan Ops",
            "marketing": "Morgan Marketing"
        }
        return agent_names.get(agent_id, agent_id.title())
    
    async def get_session(self, session_id: str) -> Dict[str, Any]:
        """Get session by ID"""
        return self.sessions.get(session_id)
    
    async def update_session_status(self, session_id: str, status: str):
        """Update session status"""
        if session_id in self.sessions:
            self.sessions[session_id]["status"] = status
            if status == "active":
                self.sessions[session_id]["started_at"] = datetime.now().isoformat()
            elif status == "completed":
                self.sessions[session_id]["completed_at"] = datetime.now().isoformat()
                # Generate mock recording URL
                self.sessions[session_id]["recording_url"] = f"https://recordings.livekit.cloud/{session_id}.mp4"
    
    async def end_session(self, session_id: str):
        """End LiveKit session"""
        if session_id in self.sessions:
            await self.update_session_status(session_id, "completed")
            
            # In real implementation, end actual LiveKit room
            # room_service = api.RoomService(self.url, self.api_key, self.api_secret)
            # await room_service.delete_room(api.DeleteRoomRequest(room=self.sessions[session_id]["room_name"]))
    
    async def add_participant(self, session_id: str, participant_id: str, participant_name: str):
        """Add participant to session"""
        if session_id in self.sessions:
            participant = {
                "id": participant_id,
                "name": participant_name,
                "role": "agent",
                "is_local": False,
                "audio_enabled": True,
                "video_enabled": True,
                "screen_share_enabled": False
            }
            self.sessions[session_id]["participants"].append(participant)
    
    async def send_message(self, session_id: str, participant_id: str, message: str):
        """Send message in session"""
        if session_id in self.sessions:
            if "messages" not in self.sessions[session_id]:
                self.sessions[session_id]["messages"] = []
            
            message_data = {
                "id": str(uuid.uuid4()),
                "participant_id": participant_id,
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "type": "chat"
            }
            
            self.sessions[session_id]["messages"].append(message_data)
            return message_data

# Global instance
livekit_service = LiveKitService()