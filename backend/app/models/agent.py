from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.sql import func
from app.core.database import Base

class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(String(50), unique=True, index=True)  # strategic, product, etc.
    name = Column(String(100), nullable=False)
    role = Column(String(100), nullable=False)
    status = Column(String(20), default="idle")  # idle, active, thinking, collaborating
    description = Column(Text)
    capabilities = Column(JSON)  # List of capabilities
    current_task = Column(Text)
    metrics = Column(JSON)  # Performance metrics
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())