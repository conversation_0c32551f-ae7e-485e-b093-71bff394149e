export interface Agent {
  id: string;
  name: string;
  role: Agent<PERSON><PERSON>;
  status: AgentStatus;
  avatar: string;
  description: string;
  capabilities: string[];
  currentTask?: string;
  metrics: AgentMetrics;
}

export type AgentRole = 
  | 'strategic' 
  | 'product' 
  | 'technical' 
  | 'operations' 
  | 'marketing';

export type AgentStatus = 
  | 'active' 
  | 'thinking' 
  | 'collaborating' 
  | 'idle';

export interface AgentMetrics {
  tasksCompleted: number;
  collaborations: number;
  successRate: number;
  uptime: string;
}

export interface Message {
  id: string;
  agentId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'code' | 'decision' | 'task';
  attachments?: Attachment[];
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  url: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'review' | 'completed';
  agents: string[];
  createdAt: Date;
  lastActivity: Date;
}