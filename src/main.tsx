console.log('🚀 main.tsx is executing');

// First, let's test if JavaScript is working at all
const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);

if (rootElement) {
  // Show immediate feedback that JavaScript is working
  rootElement.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif; background: #f0f8ff; border: 2px solid #007acc;">
      <h1 style="color: #007acc;">🚀 JavaScript is Working!</h1>
      <p>✅ main.tsx is executing</p>
      <p>✅ DOM manipulation is working</p>
      <p>⏳ Loading React components...</p>
      <div id="loading-status" style="margin-top: 20px; padding: 10px; background: #fff; border-radius: 4px;">
        <p><strong>Loading Status:</strong></p>
        <ul id="status-list">
          <li>✅ JavaScript execution: OK</li>
          <li>⏳ React imports: Loading...</li>
        </ul>
      </div>
    </div>
  `;

  // Update status
  const updateStatus = (message: string) => {
    const statusList = document.getElementById('status-list');
    if (statusList) {
      const li = document.createElement('li');
      li.textContent = message;
      statusList.appendChild(li);
    }
  };

  try {
    updateStatus('⏳ Importing React...');

    // Import React dynamically to catch any import errors
    import('react').then(React => {
      updateStatus('✅ React imported successfully');

      return import('react-dom/client');
    }).then(ReactDOM => {
      updateStatus('✅ ReactDOM imported successfully');

      return import('./App.tsx');
    }).then(AppModule => {
      updateStatus('✅ App component imported successfully');

      // Now try to render React
      const { StrictMode } = require('react');
      const { createRoot } = require('react-dom/client');
      const App = AppModule.default;

      updateStatus('⏳ Creating React root...');
      const root = createRoot(rootElement);

      updateStatus('⏳ Rendering App component...');
      root.render(
        React.createElement(StrictMode, null,
          React.createElement(App)
        )
      );

      updateStatus('✅ React app rendered successfully!');

    }).catch(error => {
      updateStatus(`❌ Error: ${error.message}`);
      console.error('Import/render error:', error);

      // Show detailed error
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = 'margin-top: 20px; padding: 15px; background: #ffebee; border: 1px solid #f44336; border-radius: 4px;';
      errorDiv.innerHTML = `
        <h3 style="color: #f44336; margin: 0 0 10px 0;">🚨 Detailed Error Information</h3>
        <p><strong>Error:</strong> ${error.message}</p>
        <p><strong>Stack:</strong></p>
        <pre style="background: #fff; padding: 10px; border-radius: 4px; overflow: auto; font-size: 12px;">${error.stack || 'No stack trace available'}</pre>
      `;
      rootElement.appendChild(errorDiv);
    });

  } catch (error) {
    updateStatus(`❌ Synchronous error: ${error.message}`);
    console.error('Synchronous error:', error);
  }

} else {
  console.error('❌ Root element not found!');
  document.body.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif; background: #ffebee; color: #f44336;">
      <h1>🚨 Critical Error</h1>
      <p>Root element with id="root" not found in the DOM!</p>
    </div>
  `;
}
