import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';

// Simple test component to debug blank screen
const SimpleApp = () => {
  console.log('SimpleApp is rendering');

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#4CAF50',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>🎉 Ultimate Co-founder App</h1>
      <p>✅ React is working!</p>
      <p>✅ Frontend server is running!</p>
      <p>✅ JavaScript is executing!</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '5px' }}>
        <h3>Next Steps:</h3>
        <ul>
          <li>Backend health check</li>
          <li>Load full application</li>
          <li>Test AI agents</li>
        </ul>
      </div>
    </div>
  );
};

console.log('main.tsx is executing');

const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);

if (rootElement) {
  const root = createRoot(rootElement);
  console.log('Root created, rendering app...');

  root.render(
    <StrictMode>
      <SimpleApp />
    </StrictMode>
  );

  console.log('App rendered');
} else {
  console.error('Root element not found!');
}
