console.log('🚀 main.tsx is executing');

const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);

if (rootElement) {
  // First, test pure DOM manipulation
  console.log('Testing pure DOM manipulation...');
  rootElement.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif; background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px;">
      <h1 style="color: #1976d2;">🚀 Pure DOM Test</h1>
      <p>✅ main.tsx is executing</p>
      <p>✅ DOM manipulation is working</p>
      <p>⏳ Now testing React imports...</p>
      <div id="react-status" style="margin-top: 20px; padding: 15px; background: #fff; border-radius: 4px;">
        <p><strong>React Import Status:</strong></p>
        <ul id="import-list">
          <li>⏳ Importing React...</li>
        </ul>
      </div>
    </div>
  `;

  const updateStatus = (message: string) => {
    const importList = document.getElementById('import-list');
    if (importList) {
      const li = document.createElement('li');
      li.textContent = message;
      importList.appendChild(li);
    }
  };

  // Test React imports step by step
  setTimeout(async () => {
    try {
      updateStatus('⏳ Importing React...');
      const React = await import('react');
      updateStatus('✅ React imported successfully');

      updateStatus('⏳ Importing ReactDOM...');
      const ReactDOM = await import('react-dom/client');
      updateStatus('✅ ReactDOM imported successfully');

      updateStatus('⏳ Creating React element...');
      const element = React.createElement('div', {
        style: { padding: '20px', background: '#c8e6c9', borderRadius: '4px', marginTop: '10px' }
      },
        React.createElement('h2', null, '🎉 React Element Created!'),
        React.createElement('p', null, '✅ React.createElement is working'),
        React.createElement('p', null, '✅ Ready to render full app')
      );
      updateStatus('✅ React element created successfully');

      updateStatus('⏳ Creating React root...');
      const root = ReactDOM.createRoot(rootElement);
      updateStatus('✅ React root created successfully');

      updateStatus('⏳ Rendering React element...');
      root.render(element);
      updateStatus('✅ React element rendered successfully!');

    } catch (error) {
      updateStatus(`❌ Error: ${error.message}`);
      console.error('React import/render error:', error);

      // Show detailed error
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = 'margin-top: 20px; padding: 15px; background: #ffcdd2; border: 1px solid #f44336; border-radius: 4px;';
      errorDiv.innerHTML = `
        <h3 style="color: #d32f2f; margin: 0 0 10px 0;">🚨 Detailed Error</h3>
        <p><strong>Message:</strong> ${error.message}</p>
        <p><strong>Stack:</strong></p>
        <pre style="background: #fff; padding: 10px; border-radius: 4px; overflow: auto; font-size: 11px; max-height: 200px;">${error.stack || 'No stack trace'}</pre>
      `;
      rootElement.appendChild(errorDiv);
    }
  }, 1000);

} else {
  console.error('❌ Root element not found!');
  document.body.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif; background: #ffebee; color: #f44336;">
      <h1>🚨 Critical Error</h1>
      <p>Root element with id="root" not found!</p>
    </div>
  `;
}
