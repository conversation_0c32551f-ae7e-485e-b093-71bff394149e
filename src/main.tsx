console.log('🚀 main.tsx is executing!');

// First test - basic DOM manipulation
const rootElement = document.getElementById('root');
if (rootElement) {
  console.log('✅ Root element found');
  rootElement.innerHTML = '<h1 style="color: red; padding: 20px;">BASIC TEST: JavaScript is working! ✅</h1>';
} else {
  console.error('❌ Root element not found');
}

// Second test - try React
try {
  console.log('🔄 Attempting to load React...');

  import { StrictMode } from 'react';
  import { createRoot } from 'react-dom/client';
  import App from './App.tsx';
  import './index.css';

  console.log('✅ React imports successful');

  setTimeout(() => {
    try {
      console.log('🔄 Creating React root...');
      const root = createRoot(document.getElementById('root')!);
      console.log('✅ React root created');

      root.render(
        <StrictMode>
          <App />
        </StrictMode>
      );
      console.log('✅ React app rendered');
    } catch (error) {
      console.error('❌ React rendering failed:', error);
    }
  }, 1000);

} catch (error) {
  console.error('❌ React import failed:', error);
}
