import axios, { AxiosInstance, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { mockApiService } from './mock-api';

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  status: string;
}

export interface AuthTokens {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
  is_active: boolean;
}

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;
  private useMockApi: boolean = false;

  constructor() {
    // Always prioritize VITE_API_URL environment variable
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 5000, // Reduced timeout to fail faster
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        // If we get a network error, switch to mock API
        if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error' || error.code === 'ECONNREFUSED') {
          console.warn('Backend server not available, switching to mock API');
          this.useMockApi = true;
          toast.info('Using demo mode - backend server not available');
          return Promise.reject(new Error('SWITCH_TO_MOCK'));
        }

        if (error.response?.status === 401) {
          this.clearAuth();
          window.location.href = '/login';
          toast.error('Session expired. Please login again.');
        } else if (error.response?.status >= 500) {
          toast.error('Server error. Please try again later.');
        } else if (error.response?.data?.detail) {
          toast.error(error.response.data.detail);
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  getToken(): string | null {
    if (this.useMockApi) {
      return mockApiService.getToken();
    }
    return localStorage.getItem('access_token');
  }

  setToken(token: string): void {
    if (this.useMockApi) {
      mockApiService.setToken(token);
    } else {
      localStorage.setItem('access_token', token);
    }
  }

  clearAuth(): void {
    if (this.useMockApi) {
      mockApiService.clearAuth();
    } else {
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
    }
  }

  isAuthenticated(): boolean {
    if (this.useMockApi) {
      return mockApiService.isAuthenticated();
    }
    return !!this.getToken();
  }

  // API methods with fallback to mock
  async get<T>(url: string): Promise<T> {
    if (this.useMockApi) {
      throw new Error('Mock API does not support generic GET requests');
    }
    
    try {
      const response = await this.api.get<T>(url);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        throw new Error('Backend not available');
      }
      throw error;
    }
  }

  async post<T>(url: string, data?: any): Promise<T> {
    if (this.useMockApi) {
      throw new Error('Mock API does not support generic POST requests');
    }
    
    try {
      const response = await this.api.post<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK') {
        throw new Error('Backend not available');
      }
      throw error;
    }
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<T>(url);
    return response.data;
  }

  // Auth API calls with mock fallback
  async login(email: string, password: string): Promise<AuthTokens> {
    try {
      const formData = new FormData();
      formData.append('username', email);
      formData.append('password', password);

      const response = await this.api.post<AuthTokens>('/api/v1/auth/login', formData, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      });
      
      this.setToken(response.data.access_token);
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        const result = await mockApiService.login(email, password);
        this.setToken(result.access_token);
        return result;
      }
      throw error;
    }
  }

  async register(email: string, password: string, name: string):  Promise<User> {
    try {
      const response = await this.api.post<User>('/api/v1/auth/register', {
        email,
        password,
        name
      });
      return response.data;
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.register(email, password, name);
      }
      throw error;
    }
  }

  async getCurrentUser(): Promise<User> {
    try {
      return await this.get<User>('/api/v1/auth/me');
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.getCurrentUser();
      }
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      if (!this.useMockApi) {
        await this.post('/api/v1/auth/logout');
      }
    } catch (error: any) {
      if (error.message !== 'SWITCH_TO_MOCK' && !this.useMockApi) {
        throw error;
      }
    } finally {
      if (this.useMockApi) {
        await mockApiService.logout();
      }
      this.clearAuth();
    }
  }

  // Health check with mock fallback
  async healthCheck(): Promise<{ status: string }> {
    try {
      return await this.get('/health');
    } catch (error: any) {
      if (error.message === 'SWITCH_TO_MOCK' || this.useMockApi) {
        return mockApiService.healthCheck();
      }
      throw error;
    }
  }
}

export const apiService = new ApiService();