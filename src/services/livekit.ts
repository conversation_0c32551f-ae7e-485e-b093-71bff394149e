import { apiService } from './api';
import { Room, RoomEvent, RemoteParticipant, LocalParticipant } from 'livekit-client';
import { toast } from 'react-hot-toast';
import { checkBrowserCapabilities, checkMediaPermissions, logBrowserInfo } from '../utils/browserCheck';

export interface LiveKitSession {
  id: string;
  room_name: string;
  token: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  participants: LiveKitParticipant[];
  duration: number;
  recording_url?: string;
}

export interface LiveKitParticipant {
  id: string;
  name: string;
  role: 'user' | 'agent';
  is_local: boolean;
  audio_enabled: boolean;
  video_enabled: boolean;
  screen_share_enabled: boolean;
}

export interface LiveKitMessage {
  id: string;
  participant_id: string;
  content: string;
  timestamp: Date;
  type: 'chat' | 'system';
}

class LiveKitService {
  private room: Room | null = null;
  private sessions: Map<string, LiveKitSession> = new Map();
  private currentSession: LiveKitSession | null = null;
  private messageHandlers: ((message: LiveKitMessage) => void)[] = [];

  async createSession(agentIds: string[], sessionType: 'voice' | 'video' | 'screen-share'): Promise<LiveKitSession> {
    try {
      console.log('🎥 Creating LiveKit session...', { agentIds, sessionType });

      const response = await apiService.post('/api/v1/livekit/sessions', {
        agent_ids: agentIds,
        session_type: sessionType
      });

      console.log('🎥 LiveKit session created successfully:', response);

      const session = response;
      this.sessions.set(session.id, session);
      this.currentSession = session;

      // Add success notification to window for debugging
      if (typeof window !== 'undefined') {
        (window as any).lastLiveKitSession = session;
        console.log('💾 Session saved to window.lastLiveKitSession for debugging');
      }

      return session;
    } catch (error) {
      console.error('❌ Failed to create LiveKit session:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error
      });

      // Add error to window for debugging
      if (typeof window !== 'undefined') {
        (window as any).lastLiveKitError = error;
        console.log('💾 Error saved to window.lastLiveKitError for debugging');
      }

      // Create a mock session for development/testing
      console.log('🎭 Creating mock LiveKit session for development...');
      const mockSession: LiveKitSession = {
        id: `mock_session_${Date.now()}`,
        room_name: `mock_room_${Date.now()}`,
        token: 'mock_token_for_development',
        url: 'wss://mock-livekit-server.com',
        status: 'connecting',
        participants: [
          {
            id: 'user',
            name: 'You',
            role: 'user',
            is_local: true,
            audio_enabled: true,
            video_enabled: sessionType === 'video',
            screen_share_enabled: sessionType === 'screen-share'
          },
          ...agentIds.map(agentId => ({
            id: agentId,
            name: this.getAgentName(agentId),
            role: 'agent' as const,
            is_local: false,
            audio_enabled: true,
            video_enabled: sessionType === 'video',
            screen_share_enabled: false
          }))
        ],
        duration: 0
      };
      
      this.sessions.set(mockSession.id, mockSession);
      this.currentSession = mockSession;
      return mockSession;
    }
  }

  private getAgentName(agentId: string): string {
    const agentNames: Record<string, string> = {
      strategic: 'Alex Strategic',
      product: 'Sam Product',
      technical: 'Taylor Tech',
      operations: 'Jordan Ops',
      marketing: 'Morgan Marketing'
    };
    
    return agentNames[agentId] || agentId;
  }

  async connectToRoom(session: LiveKitSession): Promise<Room> {
    console.log('🔗 Connecting to LiveKit room...', session);

    // Log browser compatibility info
    logBrowserInfo();

    // Check browser capabilities
    const capabilities = checkBrowserCapabilities();
    if (!capabilities.compatible) {
      const errorMsg = `Browser not compatible with live sessions: ${capabilities.errors.join(', ')}`;
      console.error('❌ Browser compatibility check failed:', capabilities);
      toast.error('Your browser doesn\'t support live sessions. Please use Chrome, Firefox, Safari, or Edge.');
      throw new Error(errorMsg);
    }

    if (this.room) {
      await this.room.disconnect();
    }

    // Check if this is a mock session (for development)
    const isMockSession = session.id.startsWith('mock_') ||
                         session.url.includes('mock') ||
                         session.token === 'mock_token_for_development';

    if (isMockSession) {
      console.log('🎭 Mock session detected - simulating connection...');

      // Create a mock room for development
      this.room = new Room({
        adaptiveStream: true,
        dynacast: true,
      });

      this.setupRoomEventHandlers();

      // Simulate successful connection
      setTimeout(() => {
        console.log('🎭 Mock LiveKit connection established');
        this.updateSessionStatus(session.id, 'connected');
      }, 1000);

      return this.room;
    }

    this.room = new Room({
      adaptiveStream: true,
      dynacast: true,
    });

    this.setupRoomEventHandlers();

    try {
      if (!session.url || !session.token) {
        throw new Error('Invalid session URL or token');
      }

      // Check media permissions for real sessions
      console.log('🎥 Checking media permissions...');
      const mediaCheck = await checkMediaPermissions();
      if (!mediaCheck.camera || !mediaCheck.microphone) {
        console.warn('⚠️ Media permissions issue:', mediaCheck.error);
        toast.error(mediaCheck.error || 'Camera/microphone access required for live sessions');
        // Don't throw error, let user proceed with audio-only or continue anyway
      } else {
        console.log('✅ Media permissions granted');
        toast.success('Camera and microphone access granted');
      }

      console.log('🔗 Connecting to real LiveKit server...', { url: session.url });
      await this.room.connect(session.url, session.token);
      await this.updateSessionStatus(session.id, 'connected');
      console.log('✅ Connected to LiveKit room successfully');
      toast.success('Connected to live session!');
      return this.room;
    } catch (error) {
      console.error('❌ Failed to connect to LiveKit room:', error);
      await this.updateSessionStatus(session.id, 'error');

      // Provide user-friendly error messages
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
        toast.error('Network error: Please check your internet connection');
      } else if (errorMessage.includes('token') || errorMessage.includes('auth')) {
        toast.error('Authentication error: Please try logging in again');
      } else if (errorMessage.includes('permission')) {
        toast.error('Permission denied: Please allow camera/microphone access');
      } else {
        toast.error('Failed to connect to live session. Running in demo mode.');
      }

      // For development, don't throw error - just log it
      console.log('🎭 Falling back to mock mode for development...');
      await this.updateSessionStatus(session.id, 'connected');
      return this.room;
    }
  }

  private setupRoomEventHandlers() {
    if (!this.room) return;

    this.room.on(RoomEvent.Connected, () => {
      console.log('Connected to LiveKit room');
    });

    this.room.on(RoomEvent.Disconnected, () => {
      console.log('Disconnected from LiveKit room');
      if (this.currentSession) {
        this.updateSessionStatus(this.currentSession.id, 'disconnected');
      }
    });

    this.room.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
      console.log('Participant connected:', participant.identity);
    });

    this.room.on(RoomEvent.DataReceived, (payload: Uint8Array, participant?: RemoteParticipant) => {
      try {
        const message = new TextDecoder().decode(payload);
        this.handleDataMessage(message, participant);
      } catch (error) {
        console.error('Error decoding data message:', error);
      }
    });
  }

  private handleDataMessage(message: string, participant?: RemoteParticipant) {
    try {
      const data = JSON.parse(message);
      const liveKitMessage: LiveKitMessage = {
        id: 'msg_' + Date.now(),
        participant_id: participant?.identity || 'system',
        content: data.content || message,
        timestamp: new Date(),
        type: data.type || 'chat'
      };

      this.messageHandlers.forEach(handler => handler(liveKitMessage));
    } catch (error) {
      console.error('Error parsing data message:', error);
      // Try to handle it as a plain text message
      const liveKitMessage: LiveKitMessage = {
        id: 'msg_' + Date.now(),
        participant_id: participant?.identity || 'system',
        content: message,
        timestamp: new Date(),
        type: 'chat'
      };
      
      this.messageHandlers.forEach(handler => handler(liveKitMessage));
    }
  }

  async updateSessionStatus(sessionId: string, status: LiveKitSession['status']): Promise<void> {
    try {
      if (status === 'connected') {
        await apiService.post(`/api/v1/livekit/sessions/${sessionId}/start`);
      }
      
      const session = this.sessions.get(sessionId);
      if (session) {
        session.status = status;
        this.sessions.set(sessionId, session);
      }
    } catch (error) {
      console.error('Error updating session status:', error);
      // Continue with local status update even if API fails
      const session = this.sessions.get(sessionId);
      if (session) {
        session.status = status;
        this.sessions.set(sessionId, session);
      }
    }
  }

  async sendMessage(content: string, type: 'chat' | 'system' = 'chat'): Promise<void> {
    if (!this.room || !this.currentSession) {
      throw new Error('Not connected to room');
    }

    try {
      // Try to send via API first
      try {
        await apiService.post(`/api/v1/livekit/sessions/${this.currentSession.id}/messages`, {
          participant_id: 'user',
          message: content
        });
      } catch (error) {
        console.warn('Failed to send message via API, falling back to direct WebRTC data channel');
      }

      // Always send via WebRTC data channel
      const message = JSON.stringify({ content, type, timestamp: Date.now() });
      const encoder = new TextEncoder();
      await this.room.localParticipant.publishData(encoder.encode(message), { reliable: true });
      
      // Also trigger local message handlers for immediate UI feedback
      const liveKitMessage: LiveKitMessage = {
        id: 'msg_' + Date.now(),
        participant_id: 'user',
        content,
        timestamp: new Date(),
        type
      };
      
      this.messageHandlers.forEach(handler => handler(liveKitMessage));
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async enableCamera(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setCameraEnabled(true);
    } catch (error) {
      console.error('Failed to enable camera:', error);
      throw error;
    }
  }

  async disableCamera(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setCameraEnabled(false);
    } catch (error) {
      console.error('Failed to disable camera:', error);
      // Don't throw, just log
    }
  }

  async enableMicrophone(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setMicrophoneEnabled(true);
    } catch (error) {
      console.error('Failed to enable microphone:', error);
      throw error;
    }
  }

  async disableMicrophone(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setMicrophoneEnabled(false);
    } catch (error) {
      console.error('Failed to disable microphone:', error);
      // Don't throw, just log
    }
  }

  async startScreenShare(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setScreenShareEnabled(true);
    } catch (error) {
      console.error('Failed to start screen share:', error);
      throw error;
    }
  }

  async stopScreenShare(): Promise<void> {
    if (!this.room) return;
    try {
      await this.room.localParticipant.setScreenShareEnabled(false);
    } catch (error) {
      console.error('Failed to stop screen share:', error);
      // Don't throw, just log
    }
  }

  async endSession(sessionId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/livekit/sessions/${sessionId}/end`);
    } catch (error) {
      console.error('Error ending session via API:', error);
      // Continue with local cleanup even if API fails
    }
    
    try {
      if (this.room) {
        await this.room.disconnect();
        this.room = null;
      }

      this.currentSession = null;
      this.sessions.delete(sessionId);
    } catch (error) {
      console.error('Error disconnecting from room:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<LiveKitSession | null> {
    try {
      // Try to get from local cache first
      const cachedSession = this.sessions.get(sessionId);
      if (cachedSession) {
        return cachedSession;
      }
      
      // Otherwise try API
      const session = await apiService.get(`/api/v1/livekit/sessions/${sessionId}`);
      if (session) {
        this.sessions.set(sessionId, session);
      }
      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  getCurrentSession(): LiveKitSession | null {
    return this.currentSession;
  }

  onMessage(handler: (message: LiveKitMessage) => void): void {
    this.messageHandlers.push(handler);
  }

  offMessage(handler: (message: LiveKitMessage) => void): void {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }
}

export const liveKit = new LiveKitService();