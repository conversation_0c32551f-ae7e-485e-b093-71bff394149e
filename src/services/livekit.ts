import { apiService } from './api';
import { Room, RoomEvent, RemoteParticipant, LocalParticipant } from 'livekit-client';

export interface LiveKitSession {
  id: string;
  room_name: string;
  token: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  participants: LiveKitParticipant[];
  duration: number;
  recording_url?: string;
}

export interface LiveKitParticipant {
  id: string;
  name: string;
  role: 'user' | 'agent';
  is_local: boolean;
  audio_enabled: boolean;
  video_enabled: boolean;
  screen_share_enabled: boolean;
}

export interface LiveKitMessage {
  id: string;
  participant_id: string;
  content: string;
  timestamp: Date;
  type: 'chat' | 'system';
}

class LiveKitService {
  private room: Room | null = null;
  private sessions: Map<string, LiveKitSession> = new Map();
  private currentSession: LiveKitSession | null = null;
  private messageHandlers: ((message: LiveKitMessage) => void)[] = [];

  async createSession(agentIds: string[], sessionType: 'voice' | 'video' | 'screen-share'): Promise<LiveKitSession> {
    try {
      const session = await apiService.post('/api/v1/livekit/sessions', {
        agent_ids: agentIds,
        session_type: sessionType
      });

      this.sessions.set(session.id, session);
      this.currentSession = session;
      return session;
    } catch (error) {
      console.error('Failed to create LiveKit session:', error);
      throw error;
    }
  }

  async connectToRoom(session: LiveKitSession): Promise<Room> {
    if (this.room) {
      await this.room.disconnect();
    }

    this.room = new Room({
      adaptiveStream: true,
      dynacast: true,
    });

    this.setupRoomEventHandlers();

    try {
      await this.room.connect(session.url, session.token);
      await this.updateSessionStatus(session.id, 'connected');
      return this.room;
    } catch (error) {
      console.error('Failed to connect to LiveKit room:', error);
      await this.updateSessionStatus(session.id, 'error');
      throw error;
    }
  }

  private setupRoomEventHandlers() {
    if (!this.room) return;

    this.room.on(RoomEvent.Connected, () => {
      console.log('Connected to LiveKit room');
    });

    this.room.on(RoomEvent.Disconnected, () => {
      console.log('Disconnected from LiveKit room');
      if (this.currentSession) {
        this.updateSessionStatus(this.currentSession.id, 'disconnected');
      }
    });

    this.room.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
      console.log('Participant connected:', participant.identity);
    });

    this.room.on(RoomEvent.DataReceived, (payload: Uint8Array, participant?: RemoteParticipant) => {
      const message = new TextDecoder().decode(payload);
      this.handleDataMessage(message, participant);
    });
  }

  private handleDataMessage(message: string, participant?: RemoteParticipant) {
    try {
      const data = JSON.parse(message);
      const liveKitMessage: LiveKitMessage = {
        id: 'msg_' + Date.now(),
        participant_id: participant?.identity || 'system',
        content: data.content || message,
        timestamp: new Date(),
        type: data.type || 'chat'
      };

      this.messageHandlers.forEach(handler => handler(liveKitMessage));
    } catch (error) {
      console.error('Error parsing data message:', error);
    }
  }

  async updateSessionStatus(sessionId: string, status: LiveKitSession['status']): Promise<void> {
    try {
      if (status === 'connected') {
        await apiService.post(`/api/v1/livekit/sessions/${sessionId}/start`);
      }
      
      const session = this.sessions.get(sessionId);
      if (session) {
        session.status = status;
        this.sessions.set(sessionId, session);
      }
    } catch (error) {
      console.error('Error updating session status:', error);
    }
  }

  async sendMessage(content: string, type: 'chat' | 'system' = 'chat'): Promise<void> {
    if (!this.room || !this.currentSession) {
      throw new Error('Not connected to room');
    }

    try {
      await apiService.post(`/api/v1/livekit/sessions/${this.currentSession.id}/messages`, {
        participant_id: 'user',
        message: content
      });

      const message = JSON.stringify({ content, type, timestamp: Date.now() });
      const encoder = new TextEncoder();
      await this.room.localParticipant.publishData(encoder.encode(message), { reliable: true });
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async enableCamera(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setCameraEnabled(true);
  }

  async disableCamera(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setCameraEnabled(false);
  }

  async enableMicrophone(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setMicrophoneEnabled(true);
  }

  async disableMicrophone(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setMicrophoneEnabled(false);
  }

  async startScreenShare(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setScreenShareEnabled(true);
  }

  async stopScreenShare(): Promise<void> {
    if (!this.room) return;
    await this.room.localParticipant.setScreenShareEnabled(false);
  }

  async endSession(sessionId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/livekit/sessions/${sessionId}/end`);
      
      if (this.room) {
        await this.room.disconnect();
        this.room = null;
      }

      this.currentSession = null;
    } catch (error) {
      console.error('Error ending session:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<LiveKitSession | null> {
    try {
      const session = await apiService.get(`/api/v1/livekit/sessions/${sessionId}`);
      return session;
    } catch (error) {
      console.error('Error getting session:', error);
      return null;
    }
  }

  getCurrentSession(): LiveKitSession | null {
    return this.currentSession;
  }

  onMessage(handler: (message: LiveKitMessage) => void): void {
    this.messageHandlers.push(handler);
  }

  offMessage(handler: (message: LiveKitMessage) => void): void {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }
}

export const liveKit = new LiveKitService();