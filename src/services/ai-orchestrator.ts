import { apiService } from './api';
import toast from 'react-hot-toast';

export interface OrchestrationTask {
  id: string;
  description: string;
  agentIds: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  results: CrewResult[];
  liveKitSession?: LiveKitSession;
  integrationActions?: ComposioAction[];
  boltProject?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface AgentSuggestion {
  id: string;
  agentId: string;
  agentName: string;
  title: string;
  description: string;
  action: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
  parameters?: Record<string, any>;
}

export interface CrewResult {
  task_id: string;
  agent_id: string;
  result: string;
  metadata: {
    execution_time: number;
    tokens_used?: number;
    model: string;
    status: string;
  };
}

export interface LiveKitSession {
  id: string;
  room_name: string;
  token: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  participants: LiveKitParticipant[];
  duration: number;
  recording_url?: string;
}

export interface LiveKitParticipant {
  id: string;
  name: string;
  role: 'user' | 'agent';
  is_local: boolean;
  audio_enabled: boolean;
  video_enabled: boolean;
  screen_share_enabled: boolean;
}

export interface ComposioAction {
  id: string;
  integration: string;
  action: string;
  parameters: Record<string, any>;
  result?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

class AIOrchestrator {
  private tasks: Map<string, OrchestrationTask> = new Map();
  private activeTask: OrchestrationTask | null = null;

  async processUserInput(input: string, selectedAgents?: string[]): Promise<{
    responses: CrewResult[];
    suggestions: AgentSuggestion[];
  }> {
    try {
      const response = await apiService.post('/api/v1/orchestrator/process', {
        description: input,
        agent_ids: selectedAgents,
        include_livekit: false
      });

      return {
        responses: response.responses || [],
        suggestions: response.suggestions || []
      };
    } catch (error) {
      console.error('Error processing user input:', error);
      toast.error('Failed to process message');
      throw error;
    }
  }

  async executeSuggestion(suggestion: AgentSuggestion): Promise<CrewResult> {
    try {
      const response = await apiService.post('/api/v1/orchestrator/execute-suggestion', {
        suggestion_id: suggestion.id,
        agent_id: suggestion.agentId,
        action: suggestion.action,
        parameters: suggestion.parameters || {}
      });

      toast.success(`${suggestion.agentName} completed: ${suggestion.title}`);
      return response.result;
    } catch (error) {
      console.error('Error executing suggestion:', error);
      toast.error(`Failed to execute: ${suggestion.title}`);
      throw error;
    }
  }

  async startLiveKitSession(agentIds: string[], sessionType: 'voice' | 'video' | 'screen-share'): Promise<LiveKitSession> {
    try {
      const session = await apiService.post('/api/v1/livekit/sessions', {
        agent_ids: agentIds,
        session_type: sessionType
      });

      toast.success('LiveKit session started');
      return session;
    } catch (error) {
      console.error('Error starting LiveKit session:', error);
      toast.error('Failed to start LiveKit session');
      throw error;
    }
  }

  async endLiveKitSession(sessionId: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/livekit/sessions/${sessionId}/end`);
      toast.success('LiveKit session ended');
    } catch (error) {
      console.error('Error ending LiveKit session:', error);
      toast.error('Failed to end LiveKit session');
    }
  }

  async syncIntegrations(): Promise<void> {
    try {
      const integrations = await apiService.get('/api/v1/integrations');
      const connectedIntegrations = integrations.integrations.filter((i: any) => i.status === 'connected');

      for (const integration of connectedIntegrations) {
        await apiService.post(`/api/v1/integrations/${integration.id}/sync`);
      }

      toast.success(`Synced ${connectedIntegrations.length} integrations`);
    } catch (error) {
      console.error('Error syncing integrations:', error);
      toast.error('Failed to sync integrations');
    }
  }

  async getOrchestratorStatus() {
    try {
      return await apiService.get('/api/v1/orchestrator/status');
    } catch (error) {
      console.error('Error getting orchestrator status:', error);
      return null;
    }
  }

  getActiveTask(): OrchestrationTask | null {
    return this.activeTask;
  }

  getTask(taskId: string): OrchestrationTask | null {
    return this.tasks.get(taskId) || null;
  }

  getAllTasks(): OrchestrationTask[] {
    return Array.from(this.tasks.values());
  }
}

export const aiOrchestrator = new AIOrchestrator();