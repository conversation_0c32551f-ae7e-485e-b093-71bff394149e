import { apiService } from './api';

export interface BoltProject {
  id: string;
  name: string;
  description: string;
  status: 'creating' | 'building' | 'ready' | 'error';
  url?: string;
  downloadUrl?: string;
  files: BoltFile[];
  createdAt: string;
  lastModified: string;
}

export interface BoltFile {
  path: string;
  content: string;
  type: 'file' | 'directory';
  size?: number;
}

export interface BoltDeployment {
  id: string;
  projectId: string;
  status: 'pending' | 'building' | 'deployed' | 'failed';
  url?: string;
  logs: string[];
  createdAt: string;
}

class BoltIntegrationService {
  async createProject(name: string, description: string, files: BoltFile[]): Promise<BoltProject> {
    try {
      const response = await apiService.post('/api/v1/bolt/projects', {
        name,
        description,
        files
      });
      return response;
    } catch (error) {
      console.error('Error creating Bolt project:', error);
      throw error;
    }
  }

  async getProject(projectId: string): Promise<BoltProject> {
    try {
      return await apiService.get(`/api/v1/bolt/projects/${projectId}`);
    } catch (error) {
      console.error(`Error getting Bolt project ${projectId}:`, error);
      throw error;
    }
  }

  async updateProject(projectId: string, files: BoltFile[]): Promise<BoltProject> {
    try {
      return await apiService.put(`/api/v1/bolt/projects/${projectId}`, {
        files
      });
    } catch (error) {
      console.error(`Error updating Bolt project ${projectId}:`, error);
      throw error;
    }
  }

  async deployProject(projectId: string): Promise<BoltDeployment> {
    try {
      return await apiService.post(`/api/v1/bolt/projects/${projectId}/deploy`);
    } catch (error) {
      console.error(`Error deploying Bolt project ${projectId}:`, error);
      throw error;
    }
  }

  async generateScaffold(prompt: string, techStack: string): Promise<BoltFile[]> {
    try {
      const response = await apiService.post('/api/v1/bolt/scaffold', {
        prompt,
        tech_stack: techStack
      });
      return response.files;
    } catch (error) {
      console.error('Error generating scaffold:', error);
      throw error;
    }
  }

  async screenShareDeploy(sessionUrl: string, files: BoltFile[]): Promise<{ success: boolean; recordingUrl?: string; projectUrl?: string }> {
    try {
      return await apiService.post('/api/v1/bolt/screen-share-deploy', {
        session_url: sessionUrl,
        files
      });
    } catch (error) {
      console.error('Error deploying via screen share:', error);
      throw error;
    }
  }

  async getDeploymentStatus(deploymentId: string): Promise<BoltDeployment> {
    try {
      return await apiService.get(`/api/v1/bolt/deployments/${deploymentId}`);
    } catch (error) {
      console.error(`Error getting deployment status for ${deploymentId}:`, error);
      throw error;
    }
  }

  async getProjects(): Promise<BoltProject[]> {
    try {
      const response = await apiService.get('/api/v1/bolt/projects');
      return response.projects;
    } catch (error) {
      console.error('Error getting Bolt projects:', error);
      throw error;
    }
  }
}

export const boltIntegration = new BoltIntegrationService();