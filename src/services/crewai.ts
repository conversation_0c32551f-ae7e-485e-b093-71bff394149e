import { apiService } from './api';

export interface Agent {
  id: string;
  name: string;
  role: string;
  goal: string;
  backstory: string;
  tools: string[];
  status: string;
}

export interface Task {
  id: string;
  description: string;
  agent: string;
  expected_output: string;
  context?: string[];
}

export interface CrewResult {
  task_id: string;
  agent_id: string;
  result: string;
  metadata: {
    execution_time: number;
    tokens_used: number;
    model: string;
  };
}

class CrewAIService {
  async executeTask(agentId: string, taskDescription: string, context?: string): Promise<CrewResult> {
    try {
      const response = await apiService.post(`/api/v1/agents/execute?agent_id=${agentId}`, {
        description: taskDescription,
        context
      });
      return response;
    } catch (error) {
      console.error(`Error executing task for agent ${agentId}:`, error);
      throw error;
    }
  }

  async executeMultiAgentTask(taskDescription: string, agentIds: string[]): Promise<CrewResult[]> {
    try {
      const response = await apiService.post('/api/v1/agents/execute-multi', {
        description: taskDescription,
        agent_ids: agentIds
      });
      return response.results;
    } catch (error) {
      console.error('Error executing multi-agent task:', error);
      throw error;
    }
  }

  async getAgents(): Promise<Agent[]> {
    try {
      const response = await apiService.get('/api/v1/agents');
      return response.agents;
    } catch (error) {
      console.error('Error getting agents:', error);
      throw error;
    }
  }

  async getAgent(agentId: string): Promise<Agent> {
    try {
      return await apiService.get(`/api/v1/agents/${agentId}`);
    } catch (error) {
      console.error(`Error getting agent ${agentId}:`, error);
      throw error;
    }
  }

  async updateAgentStatus(agentId: string, status: string): Promise<void> {
    try {
      await apiService.post(`/api/v1/agents/${agentId}/status?status=${status}`);
    } catch (error) {
      console.error(`Error updating agent ${agentId} status:`, error);
      throw error;
    }
  }
}

export const crewAI = new CrewAIService();