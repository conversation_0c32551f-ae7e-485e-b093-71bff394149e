/**
 * TopView.ai Video Creation Service
 * 
 * Frontend service for integrating with TopView.ai API through the backend.
 * Provides video creation capabilities including URL-to-Video, Video Avatar,
 * Product Avatar, Materials-to-Video, and Image-to-Video.
 */

import { apiService } from './api';

export interface TopViewTask {
  taskId: string;
  status: 'pending' | 'processing' | 'success' | 'failed' | 'error';
  message: string;
  videoType: string;
  createdAt: string;
}

export interface VideoResult {
  taskId: string;
  status: string;
  videoUrl?: string;
  coverUrl?: string;
  videoDuration?: number;
  previewVideos?: Array<{
    scriptId: number;
    status: string;
    videoUrl?: string;
    coverUrl?: string;
    videoDuration?: number;
  }>;
  errorMsg?: string;
}

export interface Voice {
  id: string;
  name: string;
  language: string;
  gender: string;
}

export interface Avatar {
  id: string;
  name: string;
  ethnicity: string;
  gender: string;
}

export interface URLToVideoOptions {
  url: string;
  duration?: number;
  aspectRatio?: string;
  voiceId?: string;
  avatarId?: string;
  title?: string;
  description?: string;
}

export interface VideoAvatarOptions {
  script: string;
  avatarId?: string;
  voiceId?: string;
  backgroundType?: string;
  backgroundColor?: string;
  duration?: number;
}

export interface ProductAvatarOptions {
  productImageUrl: string;
  avatarCategory?: string;
  ethnicity?: string;
  gender?: string;
  script?: string;
}

export interface MaterialsToVideoOptions {
  materials: string[];
  script: string;
  videoType?: string;
  duration?: number;
  voiceId?: string;
  title?: string;
}

export interface ImageToVideoOptions {
  imageUrl: string;
  duration?: number;
  motionStrength?: 'low' | 'medium' | 'high';
}

class TopViewService {
  private baseUrl = '/api/v1/topview';

  /**
   * Create video from URL using TopView.ai URL-to-Video API
   */
  async createURLToVideo(options: URLToVideoOptions): Promise<TopViewTask> {
    console.log('🎬 Creating URL-to-Video:', options.url);
    
    try {
      const response = await apiService.post(`${this.baseUrl}/url-to-video`, options);
      console.log('✅ URL-to-Video task created:', response.taskId);
      return response;
    } catch (error) {
      console.error('❌ URL-to-Video creation failed:', error);
      throw new Error(`Failed to create URL-to-Video: ${error}`);
    }
  }

  /**
   * Create video with AI avatar using TopView.ai Video Avatar API
   */
  async createVideoAvatar(options: VideoAvatarOptions): Promise<TopViewTask> {
    console.log('🎭 Creating Video Avatar with script length:', options.script.length);
    
    try {
      const response = await apiService.post(`${this.baseUrl}/video-avatar`, options);
      console.log('✅ Video Avatar task created:', response.taskId);
      return response;
    } catch (error) {
      console.error('❌ Video Avatar creation failed:', error);
      throw new Error(`Failed to create Video Avatar: ${error}`);
    }
  }

  /**
   * Create product avatar video using TopView.ai Product Avatar API
   */
  async createProductAvatar(options: ProductAvatarOptions): Promise<TopViewTask> {
    console.log('🛍️ Creating Product Avatar for:', options.productImageUrl);
    
    try {
      const response = await apiService.post(`${this.baseUrl}/product-avatar`, options);
      console.log('✅ Product Avatar task created:', response.taskId);
      return response;
    } catch (error) {
      console.error('❌ Product Avatar creation failed:', error);
      throw new Error(`Failed to create Product Avatar: ${error}`);
    }
  }

  /**
   * Create video from materials using TopView.ai Materials-to-Video API
   */
  async createMaterialsToVideo(options: MaterialsToVideoOptions): Promise<TopViewTask> {
    console.log('📁 Creating Materials-to-Video with', options.materials.length, 'materials');
    
    try {
      const response = await apiService.post(`${this.baseUrl}/materials-to-video`, options);
      console.log('✅ Materials-to-Video task created:', response.taskId);
      return response;
    } catch (error) {
      console.error('❌ Materials-to-Video creation failed:', error);
      throw new Error(`Failed to create Materials-to-Video: ${error}`);
    }
  }

  /**
   * Create video from image using TopView.ai Image2Video API
   */
  async createImageToVideo(options: ImageToVideoOptions): Promise<TopViewTask> {
    console.log('🖼️ Creating Image-to-Video for:', options.imageUrl);
    
    try {
      const response = await apiService.post(`${this.baseUrl}/image-to-video`, options);
      console.log('✅ Image-to-Video task created:', response.taskId);
      return response;
    } catch (error) {
      console.error('❌ Image-to-Video creation failed:', error);
      throw new Error(`Failed to create Image-to-Video: ${error}`);
    }
  }

  /**
   * Query task status and get video results
   */
  async queryTask(taskId: string): Promise<VideoResult> {
    console.log('🔍 Querying task:', taskId);
    
    try {
      const response = await apiService.get(`${this.baseUrl}/task/${taskId}`);
      console.log('✅ Task query result:', response.status);
      return response;
    } catch (error) {
      console.error('❌ Task query failed:', error);
      throw new Error(`Failed to query task: ${error}`);
    }
  }

  /**
   * Poll task until completion with timeout
   */
  async waitForTaskCompletion(
    taskId: string, 
    maxWaitTime: number = 300000, // 5 minutes
    pollInterval: number = 5000 // 5 seconds
  ): Promise<VideoResult> {
    console.log(`⏳ Waiting for task completion: ${taskId}`);
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        const result = await this.queryTask(taskId);
        
        if (result.status === 'success') {
          console.log('🎉 Task completed successfully:', taskId);
          return result;
        } else if (result.status === 'failed' || result.status === 'error') {
          throw new Error(`Task failed: ${result.errorMsg || 'Unknown error'}`);
        }
        
        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.error('❌ Error polling task:', error);
        throw error;
      }
    }
    
    throw new Error(`Task timeout: ${taskId} did not complete within ${maxWaitTime}ms`);
  }

  /**
   * Get available voices for video creation
   */
  async getAvailableVoices(): Promise<Voice[]> {
    console.log('🎤 Getting available voices');
    
    try {
      const response = await apiService.get(`${this.baseUrl}/voices`);
      return response.voices || [];
    } catch (error) {
      console.error('❌ Failed to get voices:', error);
      // Return default voices for development
      return [
        { id: 'default', name: 'Default Voice', language: 'en', gender: 'female' },
        { id: 'male_1', name: 'Professional Male', language: 'en', gender: 'male' },
        { id: 'female_1', name: 'Friendly Female', language: 'en', gender: 'female' },
        { id: 'narrator', name: 'Documentary Narrator', language: 'en', gender: 'male' }
      ];
    }
  }

  /**
   * Get available AI avatars for video creation
   */
  async getAvailableAvatars(): Promise<Avatar[]> {
    console.log('👤 Getting available avatars');
    
    try {
      const response = await apiService.get(`${this.baseUrl}/avatars`);
      return response.avatars || [];
    } catch (error) {
      console.error('❌ Failed to get avatars:', error);
      // Return default avatars for development
      return [
        { id: 'default', name: 'Professional Woman', ethnicity: 'mixed', gender: 'female' },
        { id: 'male_1', name: 'Business Man', ethnicity: 'caucasian', gender: 'male' },
        { id: 'female_2', name: 'Tech Presenter', ethnicity: 'asian', gender: 'female' },
        { id: 'narrator', name: 'Documentary Host', ethnicity: 'mixed', gender: 'male' }
      ];
    }
  }

  /**
   * Get TopView.ai service health status
   */
  async getHealthStatus(): Promise<any> {
    console.log('🏥 Checking TopView.ai service health');
    
    try {
      const response = await apiService.get(`${this.baseUrl}/health`);
      return response;
    } catch (error) {
      console.error('❌ Health check failed:', error);
      throw new Error(`Health check failed: ${error}`);
    }
  }

  /**
   * Create video with smart type detection
   */
  async createVideo(input: {
    type: 'url' | 'avatar' | 'product' | 'materials' | 'image';
    data: any;
    options?: any;
  }): Promise<TopViewTask> {
    console.log('🎬 Creating video with smart detection:', input.type);
    
    switch (input.type) {
      case 'url':
        return this.createURLToVideo({ url: input.data, ...input.options });
      
      case 'avatar':
        return this.createVideoAvatar({ script: input.data, ...input.options });
      
      case 'product':
        return this.createProductAvatar({ productImageUrl: input.data, ...input.options });
      
      case 'materials':
        return this.createMaterialsToVideo({ 
          materials: input.data.materials, 
          script: input.data.script, 
          ...input.options 
        });
      
      case 'image':
        return this.createImageToVideo({ imageUrl: input.data, ...input.options });
      
      default:
        throw new Error(`Unsupported video type: ${input.type}`);
    }
  }
}

// Export singleton instance
export const topViewService = new TopViewService();
