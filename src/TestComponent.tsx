import React, { useEffect, useState } from 'react';

export const TestComponent: React.FC = () => {
  const [status, setStatus] = useState('Testing...');

  useEffect(() => {
    console.log('🧪 TestComponent mounted');
    testBackendConnection();
  }, []);

  const testBackendConnection = async () => {
    try {
      console.log('🧪 Testing direct fetch to backend...');
      const response = await fetch('http://localhost:8000/health');
      const data = await response.json();
      console.log('🧪 Direct fetch successful:', data);
      setStatus(`✅ Backend connected: ${data.status}`);
    } catch (error) {
      console.error('🧪 Direct fetch failed:', error);
      setStatus(`❌ Backend connection failed: ${error}`);
    }
  };

  return (
    <div style={{ padding: '20px', fontSize: '18px', fontFamily: 'Arial' }}>
      <h1>Frontend Test</h1>
      <p>Status: {status}</p>
      <button onClick={testBackendConnection}>Test Again</button>
    </div>
  );
};
