import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { topViewService, TopViewTask, VideoResult, Voice, Avatar } from '../services/topview';

interface TopViewVideoCreatorProps {
  onVideoCreated?: (result: VideoResult) => void;
  className?: string;
}

type VideoType = 'url' | 'avatar' | 'product' | 'materials' | 'image';

export const TopViewVideoCreator: React.FC<TopViewVideoCreatorProps> = ({
  onVideoCreated,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<VideoType>('url');
  const [isCreating, setIsCreating] = useState(false);
  const [currentTask, setCurrentTask] = useState<TopViewTask | null>(null);
  const [videoResult, setVideoResult] = useState<VideoResult | null>(null);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [avatars, setAvatars] = useState<Avatar[]>([]);

  // Form states
  const [urlInput, setUrlInput] = useState('');
  const [scriptInput, setScriptInput] = useState('');
  const [productImageUrl, setProductImageUrl] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [selectedVoice, setSelectedVoice] = useState('default');
  const [selectedAvatar, setSelectedAvatar] = useState('default');
  const [materials, setMaterials] = useState<string[]>(['']);

  useEffect(() => {
    loadVoicesAndAvatars();
  }, []);

  const loadVoicesAndAvatars = async () => {
    try {
      const [voicesData, avatarsData] = await Promise.all([
        topViewService.getAvailableVoices(),
        topViewService.getAvailableAvatars()
      ]);
      setVoices(voicesData);
      setAvatars(avatarsData);
    } catch (error) {
      console.error('Failed to load voices and avatars:', error);
    }
  };

  const handleCreateVideo = async () => {
    if (isCreating) return;

    setIsCreating(true);
    setCurrentTask(null);
    setVideoResult(null);

    try {
      let task: TopViewTask;

      switch (activeTab) {
        case 'url':
          if (!urlInput.trim()) {
            toast.error('Please enter a URL');
            return;
          }
          task = await topViewService.createURLToVideo({
            url: urlInput,
            voiceId: selectedVoice,
            avatarId: selectedAvatar,
            duration: 30,
            aspectRatio: '16:9'
          });
          break;

        case 'avatar':
          if (!scriptInput.trim()) {
            toast.error('Please enter a script');
            return;
          }
          task = await topViewService.createVideoAvatar({
            script: scriptInput,
            avatarId: selectedAvatar,
            voiceId: selectedVoice,
            backgroundType: 'solid',
            backgroundColor: '#ffffff'
          });
          break;

        case 'product':
          if (!productImageUrl.trim()) {
            toast.error('Please enter a product image URL');
            return;
          }
          task = await topViewService.createProductAvatar({
            productImageUrl: productImageUrl,
            avatarCategory: 'general',
            ethnicity: 'mixed',
            gender: 'mixed',
            script: scriptInput || 'Check out this amazing product!'
          });
          break;

        case 'materials':
          const validMaterials = materials.filter(m => m.trim());
          if (validMaterials.length === 0) {
            toast.error('Please add at least one material URL');
            return;
          }
          if (!scriptInput.trim()) {
            toast.error('Please enter a script');
            return;
          }
          task = await topViewService.createMaterialsToVideo({
            materials: validMaterials,
            script: scriptInput,
            voiceId: selectedVoice,
            duration: 30
          });
          break;

        case 'image':
          if (!imageUrl.trim()) {
            toast.error('Please enter an image URL');
            return;
          }
          task = await topViewService.createImageToVideo({
            imageUrl: imageUrl,
            duration: 5,
            motionStrength: 'medium'
          });
          break;

        default:
          throw new Error('Invalid video type');
      }

      setCurrentTask(task);
      toast.success('Video creation started! Please wait...');

      // Poll for completion
      const result = await topViewService.waitForTaskCompletion(task.taskId, 300000, 5000);
      setVideoResult(result);
      
      if (result.status === 'success') {
        toast.success('Video created successfully!');
        onVideoCreated?.(result);
      } else {
        toast.error(`Video creation failed: ${result.errorMsg || 'Unknown error'}`);
      }

    } catch (error) {
      console.error('Video creation failed:', error);
      toast.error(`Video creation failed: ${error}`);
    } finally {
      setIsCreating(false);
    }
  };

  const addMaterial = () => {
    setMaterials([...materials, '']);
  };

  const updateMaterial = (index: number, value: string) => {
    const newMaterials = [...materials];
    newMaterials[index] = value;
    setMaterials(newMaterials);
  };

  const removeMaterial = (index: number) => {
    if (materials.length > 1) {
      setMaterials(materials.filter((_, i) => i !== index));
    }
  };

  const tabs = [
    { id: 'url', label: '🔗 URL to Video', description: 'Convert any URL into a video' },
    { id: 'avatar', label: '🎭 Video Avatar', description: 'Create AI avatar videos' },
    { id: 'product', label: '🛍️ Product Avatar', description: 'Product presentation videos' },
    { id: 'materials', label: '📁 Materials to Video', description: 'Create from multiple materials' },
    { id: 'image', label: '🖼️ Image to Video', description: 'Animate static images' }
  ];

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🎬 TopView.ai Video Creator
        </h2>
        <p className="text-gray-600">
          Create professional AI-powered videos with TopView.ai integration
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-2 mb-6 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as VideoType)}
            className={`px-4 py-2 rounded-t-lg font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-blue-500 text-white border-b-2 border-blue-500'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            title={tab.description}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Form Content */}
      <div className="space-y-4 mb-6">
        {activeTab === 'url' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website URL
            </label>
            <input
              type="url"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder="https://example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}

        {activeTab === 'avatar' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Script
            </label>
            <textarea
              value={scriptInput}
              onChange={(e) => setScriptInput(e.target.value)}
              placeholder="Enter the script for your AI avatar to speak..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}

        {activeTab === 'product' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Image URL
              </label>
              <input
                type="url"
                value={productImageUrl}
                onChange={(e) => setProductImageUrl(e.target.value)}
                placeholder="https://example.com/product-image.jpg"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Script (Optional)
              </label>
              <textarea
                value={scriptInput}
                onChange={(e) => setScriptInput(e.target.value)}
                placeholder="Describe your product... (optional)"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}

        {activeTab === 'materials' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Material URLs (Images/Videos)
              </label>
              {materials.map((material, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <input
                    type="url"
                    value={material}
                    onChange={(e) => updateMaterial(index, e.target.value)}
                    placeholder="https://example.com/material.jpg"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {materials.length > 1 && (
                    <button
                      onClick={() => removeMaterial(index)}
                      className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      ✕
                    </button>
                  )}
                </div>
              ))}
              <button
                onClick={addMaterial}
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                + Add Material
              </button>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Script
              </label>
              <textarea
                value={scriptInput}
                onChange={(e) => setScriptInput(e.target.value)}
                placeholder="Enter the script for your video..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        )}

        {activeTab === 'image' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Image URL
            </label>
            <input
              type="url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}

        {/* Voice and Avatar Selection */}
        {(activeTab === 'url' || activeTab === 'avatar' || activeTab === 'materials') && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Voice
              </label>
              <select
                value={selectedVoice}
                onChange={(e) => setSelectedVoice(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {voices.map((voice) => (
                  <option key={voice.id} value={voice.id}>
                    {voice.name} ({voice.gender})
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Avatar
              </label>
              <select
                value={selectedAvatar}
                onChange={(e) => setSelectedAvatar(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {avatars.map((avatar) => (
                  <option key={avatar.id} value={avatar.id}>
                    {avatar.name} ({avatar.gender})
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Create Button */}
      <button
        onClick={handleCreateVideo}
        disabled={isCreating}
        className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {isCreating ? '🎬 Creating Video...' : '🚀 Create Video'}
      </button>

      {/* Task Status */}
      {currentTask && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Task Status</h3>
          <p className="text-blue-800">
            <strong>Task ID:</strong> {currentTask.taskId}
          </p>
          <p className="text-blue-800">
            <strong>Status:</strong> {currentTask.status}
          </p>
          <p className="text-blue-800">
            <strong>Type:</strong> {currentTask.videoType}
          </p>
        </div>
      )}

      {/* Video Result */}
      {videoResult && videoResult.status === 'success' && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-4">✅ Video Created Successfully!</h3>
          
          {videoResult.videoUrl && (
            <div className="mb-4">
              <video
                controls
                className="w-full max-w-md mx-auto rounded-lg"
                poster={videoResult.coverUrl}
              >
                <source src={videoResult.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          )}
          
          <div className="space-y-2 text-sm text-green-800">
            <p><strong>Task ID:</strong> {videoResult.taskId}</p>
            {videoResult.videoDuration && (
              <p><strong>Duration:</strong> {Math.round(videoResult.videoDuration / 1000)}s</p>
            )}
            {videoResult.videoUrl && (
              <p>
                <strong>Video URL:</strong>{' '}
                <a 
                  href={videoResult.videoUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  Download Video
                </a>
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
