import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingUp, 
  Target, 
  Users, 
  Lightbulb, 
  Calendar,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Download,
  FileText,
  BarChart3,
  Globe,
  Zap
} from 'lucide-react';
import { LiveKitRoom, useVoiceAssistant, BarVisualizer } from '@livekit/components-react';
import '@livekit/components-styles';

interface MarketAnalysis {
  tam: string;
  sam: string;
  som: string;
  competitors: Array<{
    name: string;
    type: 'direct' | 'indirect';
    description: string;
    marketShare?: string;
  }>;
  whiteSpaceOpportunities: string[];
  gtmRoadmap: {
    month1_2: string[];
    month3_4: string[];
    month5_6: string[];
  };
}

interface LiveKitSession {
  url: string;
  token: string;
  isActive: boolean;
  transcript: string[];
  duration: number;
}

export const StrategicAgent: React.FC = () => {
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [userIdea, setUserIdea] = useState('');
  const [analysis, setAnalysis] = useState<MarketAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [sessionData, setSessionData] = useState<LiveKitSession>({
    url: '',
    token: '',
    isActive: false,
    transcript: [],
    duration: 0
  });
  const [currentStep, setCurrentStep] = useState<'intro' | 'session' | 'analysis' | 'complete'>('intro');

  // Simulate LiveKit session initialization
  const initializeLiveKitSession = async () => {
    setIsSessionActive(true);
    setCurrentStep('session');
    
    // Simulate session URL and token generation
    setSessionData({
      url: 'wss://strategic-cofounder.livekit.cloud',
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      isActive: true,
      transcript: [
        "Strategic Agent: Hello! I'm Alex, your Strategic Co-founder. I'm here to help analyze your business idea and develop a comprehensive market strategy.",
        "Strategic Agent: Could you please share your business idea with me? I'll listen carefully and ask clarifying questions to ensure I understand your vision completely."
      ],
      duration: 0
    });

    // Simulate 2-minute session timer
    const timer = setInterval(() => {
      setSessionData(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    // Auto-end session after 2 minutes (120 seconds)
    setTimeout(() => {
      clearInterval(timer);
      endSession();
    }, 120000);
  };

  const endSession = () => {
    setIsSessionActive(false);
    setSessionData(prev => ({ ...prev, isActive: false }));
    setCurrentStep('analysis');
    performMarketAnalysis();
  };

  const performMarketAnalysis = async () => {
    setIsAnalyzing(true);
    
    // Simulate AI analysis with realistic delay
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const mockAnalysis: MarketAnalysis = {
      tam: "$2.3T - Global Software Market",
      sam: "$180B - AI-Powered Business Tools",
      som: "$2.1B - AI Co-founder & Business Intelligence Tools",
      competitors: [
        {
          name: "Jasper AI",
          type: "indirect",
          description: "AI content creation and marketing assistant",
          marketShare: "15%"
        },
        {
          name: "Copy.ai",
          type: "indirect", 
          description: "AI writing and content generation platform",
          marketShare: "12%"
        },
        {
          name: "Notion AI",
          type: "indirect",
          description: "Integrated AI workspace and productivity tools",
          marketShare: "8%"
        },
        {
          name: "Harvey AI",
          type: "direct",
          description: "AI legal co-pilot for professional services",
          marketShare: "3%"
        },
        {
          name: "Anthropic Claude",
          type: "indirect",
          description: "General-purpose AI assistant for business",
          marketShare: "5%"
        }
      ],
      whiteSpaceOpportunities: [
        "Multi-agent collaboration systems with specialized business roles",
        "Real-time video/voice AI co-founder interactions",
        "Integrated code generation with business strategy alignment"
      ],
      gtmRoadmap: {
        month1_2: [
          "MVP development with core 5-agent system",
          "Beta testing with 50 early-stage startups",
          "LiveKit integration for seamless video/voice",
          "Initial Composio integrations (Slack, GitHub)"
        ],
        month3_4: [
          "Public launch with freemium model",
          "Content marketing and thought leadership",
          "Partnership with accelerators and VCs",
          "Advanced analytics and reporting features"
        ],
        month5_6: [
          "Enterprise tier with custom agent training",
          "API marketplace for third-party integrations",
          "International expansion (EU, APAC)",
          "Series A fundraising preparation"
        ]
      }
    };

    setAnalysis(mockAnalysis);
    setIsAnalyzing(false);
    setCurrentStep('complete');
  };

  const generateReport = () => {
    if (!analysis) return '';

    return `# Strategic Market Analysis Report

## Executive Summary
Based on our LiveKit session and comprehensive market research, here's your strategic analysis for the Ultimate Startup Co-founder platform.

## Market Sizing (TAM/SAM/SOM)
- **TAM (Total Addressable Market):** ${analysis.tam}
- **SAM (Serviceable Addressable Market):** ${analysis.sam}  
- **SOM (Serviceable Obtainable Market):** ${analysis.som}

## Competitive Landscape

### Direct Competitors
${analysis.competitors.filter(c => c.type === 'direct').map(c => 
  `- **${c.name}** (${c.marketShare}): ${c.description}`
).join('\n')}

### Indirect Competitors  
${analysis.competitors.filter(c => c.type === 'indirect').map(c => 
  `- **${c.name}** (${c.marketShare}): ${c.description}`
).join('\n')}

## White-Space Opportunities
${analysis.whiteSpaceOpportunities.map((opp, i) => `${i + 1}. ${opp}`).join('\n')}

## 6-Month Go-to-Market Roadmap

### Month 1-2: Foundation & Beta
${analysis.gtmRoadmap.month1_2.map(item => `- ${item}`).join('\n')}

### Month 3-4: Launch & Growth
${analysis.gtmRoadmap.month3_4.map(item => `- ${item}`).join('\n')}

### Month 5-6: Scale & Expansion
${analysis.gtmRoadmap.month5_6.map(item => `- ${item}`).join('\n')}

---
*Generated by Strategic Co-founder Agent | Session Duration: ${Math.floor(sessionData.duration / 60)}:${(sessionData.duration % 60).toString().padStart(2, '0')}*`;
  };

  const downloadReport = () => {
    const report = generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'strategic-market-analysis.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <TrendingUp className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Strategic Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Rapid market strategy with LiveKit voice briefing
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {[
          { id: 'intro', label: 'Introduction', icon: Users },
          { id: 'session', label: 'Voice Session', icon: Mic },
          { id: 'analysis', label: 'Analysis', icon: BarChart3 },
          { id: 'complete', label: 'Report', icon: FileText }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              currentStep === step.id 
                ? 'bg-purple-600 text-white' 
                : index < ['intro', 'session', 'analysis', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={16} />
            </div>
            <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              {step.label}
            </span>
            {index < 3 && (
              <div className={`w-8 h-0.5 mx-4 ${
                index < ['intro', 'session', 'analysis', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Let's Analyze Your Business Idea
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll conduct a 2-minute voice session to understand your idea, then provide comprehensive market analysis including TAM/SAM/SOM, competitive landscape, and a 6-month GTM roadmap.
            </p>
            
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Brief Description (Optional)
                </label>
                <textarea
                  value={userIdea}
                  onChange={(e) => setUserIdea(e.target.value)}
                  placeholder="Briefly describe your business idea to help me prepare better questions..."
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  rows={3}
                />
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={initializeLiveKitSession}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg font-semibold text-lg transition-all"
            >
              <Phone size={20} />
              Start LiveKit Voice Session
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'session' && (
          <motion.div
            key="session"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Mic className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Live Voice Session Active
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(sessionData.duration / 60)}:{(sessionData.duration % 60).toString().padStart(2, '0')} / 2:00
              </p>
            </div>

            {/* Voice Visualizer Simulation */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-center space-x-1 h-16">
                {[...Array(20)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-1 bg-green-400 rounded-full"
                    animate={{
                      height: [4, Math.random() * 40 + 10, 4],
                    }}
                    transition={{
                      duration: 0.5,
                      repeat: Infinity,
                      delay: i * 0.1,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Live Transcript */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 max-h-40 overflow-y-auto">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Live Transcript</h3>
              <div className="space-y-2">
                {sessionData.transcript.map((message, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-400">
                    {message}
                  </p>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={endSession}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
              >
                <PhoneOff size={16} />
                End Session Early
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <MicOff size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'analysis' && (
          <motion.div
            key="analysis"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center"
          >
            <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              {isAnalyzing ? (
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <BarChart3 className="w-8 h-8 text-white" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {isAnalyzing ? 'Analyzing Market Data...' : 'Analysis Complete'}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {isAnalyzing 
                ? 'Processing your session data and conducting comprehensive market research...'
                : 'Your strategic market analysis is ready for review.'
              }
            </p>
            
            {isAnalyzing && (
              <div className="space-y-2">
                {[
                  'Calculating TAM/SAM/SOM...',
                  'Identifying competitors...',
                  'Finding white-space opportunities...',
                  'Building GTM roadmap...'
                ].map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.5 }}
                    className="text-sm text-gray-500 dark:text-gray-400"
                  >
                    {step}
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        )}

        {currentStep === 'complete' && analysis && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Market Sizing */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <Target className="w-6 h-6 text-blue-500" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Market Sizing</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">TAM</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">{analysis.tam}</p>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <p className="text-sm font-medium text-purple-600 dark:text-purple-400">SAM</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">{analysis.sam}</p>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">SOM</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">{analysis.som}</p>
                </div>
              </div>
            </div>

            {/* Competitors */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <Users className="w-6 h-6 text-red-500" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">Competitive Landscape</h3>
              </div>
              <div className="space-y-3">
                {analysis.competitors.map((competitor, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">{competitor.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          competitor.type === 'direct' 
                            ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                            : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                        }`}>
                          {competitor.type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{competitor.description}</p>
                    </div>
                    {competitor.marketShare && (
                      <div className="text-right">
                        <p className="font-bold text-gray-900 dark:text-white">{competitor.marketShare}</p>
                        <p className="text-xs text-gray-500">market share</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* White-Space Opportunities */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <Lightbulb className="w-6 h-6 text-yellow-500" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">White-Space Opportunities</h3>
              </div>
              <div className="space-y-2">
                {analysis.whiteSpaceOpportunities.map((opportunity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <div className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold mt-0.5">
                      {index + 1}
                    </div>
                    <p className="text-gray-700 dark:text-gray-300">{opportunity}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* GTM Roadmap */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="w-6 h-6 text-green-500" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">6-Month GTM Roadmap</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Month 1-2: Foundation</h4>
                  <ul className="space-y-2">
                    {analysis.gtmRoadmap.month1_2.map((item, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Month 3-4: Launch</h4>
                  <ul className="space-y-2">
                    {analysis.gtmRoadmap.month3_4.map((item, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Month 5-6: Scale</h4>
                  <ul className="space-y-2">
                    {analysis.gtmRoadmap.month5_6.map((item, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Full Report
              </motion.button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setAnalysis(null);
                  setSessionData(prev => ({ ...prev, transcript: [], duration: 0 }));
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                New Analysis
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};