import React from 'react';
import { motion } from 'framer-motion';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { 
  Code2, 
  Play, 
  Download, 
  Copy, 
  Zap, 
  FileCode,
  Github,
  ExternalLink
} from 'lucide-react';

export const CodeGenerationPanel: React.FC = () => {
  const [generatedCode, setGeneratedCode] = React.useState('');
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [codeType, setCodeType] = React.useState<'react' | 'api' | 'database' | 'config'>('react');

  const codeExamples = {
    react: `import React from 'react';
import { motion } from 'framer-motion';

const Dashboard = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6"
    >
      <h1>Generated Component</h1>
    </motion.div>
  );
};

export default Dashboard;`,
    api: `// Generated API endpoint
export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Process data here
    const result = await processUserRequest(data);
    
    return Response.json({ 
      success: true, 
      data: result 
    });
  } catch (error) {
    return Response.json({ 
      error: 'Processing failed' 
    }, { status: 500 });
  }
}`,
    database: `-- Generated database schema
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Add policies
CREATE POLICY "Users can read own data"
  ON users FOR SELECT
  TO authenticated
  USING (auth.uid() = id);`,
    config: `{
  "name": "generated-project",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.3.1",
    "typescript": "^5.5.3",
    "@supabase/supabase-js": "^2.0.0"
  },
  "scripts": {
    "dev": "vite",
    "build": "vite build"
  }
}`
  };

  const generateCode = async () => {
    setIsGenerating(true);
    // Simulate code generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setGeneratedCode(codeExamples[codeType]);
    setIsGenerating(false);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
  };

  const openInBolt = () => {
    // Simulate opening in Bolt.new
    window.open('https://bolt.new', '_blank');
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Code2 className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Code Generation
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Generate code with AI assistance
        </p>
      </div>

      {/* Code Type Selector */}
      <div className="grid grid-cols-2 gap-2">
        {Object.keys(codeExamples).map((type) => (
          <button
            key={type}
            onClick={() => setCodeType(type as any)}
            className={`p-3 text-left rounded-lg transition-colors capitalize ${
              codeType === type
                ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-2 border-purple-500'
                : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
          >
            <FileCode size={16} className="mb-1" />
            <span className="text-sm font-medium">{type}</span>
          </button>
        ))}
      </div>

      {/* Generate Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={generateCode}
        disabled={isGenerating}
        className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isGenerating ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            Generating...
          </>
        ) : (
          <>
            <Zap size={16} />
            Generate {codeType} Code
          </>
        )}
      </motion.button>

      {/* Code Output */}
      {generatedCode && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="space-y-3"
        >
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 dark:text-white">
              Generated Code
            </h4>
            <div className="flex items-center gap-2">
              <button
                onClick={copyToClipboard}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <Copy size={16} />
              </button>
              <button
                onClick={openInBolt}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ExternalLink size={16} />
              </button>
            </div>
          </div>

          <div className="rounded-lg overflow-hidden">
            <SyntaxHighlighter
              language={codeType === 'database' ? 'sql' : codeType === 'config' ? 'json' : 'typescript'}
              style={vscDarkPlus}
              customStyle={{
                margin: 0,
                fontSize: '0.875rem',
                maxHeight: '300px'
              }}
            >
              {generatedCode}
            </SyntaxHighlighter>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors">
              <Play size={14} />
              Deploy to Bolt
            </button>
            <button className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors">
              <Github size={14} />
              Push to Git
            </button>
            <button className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">
              <Download size={14} />
              Download
            </button>
          </div>
        </motion.div>
      )}

      {/* Quick Templates */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Templates</h4>
        <div className="space-y-2">
          {[
            'React Dashboard',
            'REST API',
            'Database Schema',
            'Authentication Flow'
          ].map((template) => (
            <button
              key={template}
              className="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              <p className="font-medium text-gray-900 dark:text-white text-sm">{template}</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Generate {template.toLowerCase()}
              </p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};