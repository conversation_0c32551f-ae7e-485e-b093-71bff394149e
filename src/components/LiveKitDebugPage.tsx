import React, { useState, useEffect } from 'react';
import { checkBrowserCapabilities, checkMediaPermissions, getCompatibilityMessage } from '../utils/browserCheck';
import { liveKit } from '../services/livekit';
import { aiOrchestrator } from '../services/ai-orchestrator';
import { toast } from 'react-hot-toast';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: any;
}

export const LiveKitDebugPage: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Browser Compatibility', status: 'pending', message: 'Checking browser capabilities...' },
    { name: 'Media Permissions', status: 'pending', message: 'Checking camera/microphone access...' },
    { name: 'Session Creation', status: 'pending', message: 'Testing session creation...' },
    { name: 'Room Connection', status: 'pending', message: 'Testing room connection...' }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentSession, setCurrentSession] = useState<any>(null);

  const updateTest = (index: number, status: 'success' | 'error', message: string, details?: any) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, details } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Browser Compatibility
      console.log('🧪 Running browser compatibility test...');
      const capabilities = checkBrowserCapabilities();
      const compatMessage = getCompatibilityMessage(capabilities);
      
      if (capabilities.compatible) {
        updateTest(0, 'success', compatMessage, capabilities);
      } else {
        updateTest(0, 'error', compatMessage, capabilities);
        setIsRunning(false);
        return;
      }

      // Test 2: Media Permissions
      console.log('🧪 Running media permissions test...');
      try {
        const mediaCheck = await checkMediaPermissions();
        if (mediaCheck.camera && mediaCheck.microphone) {
          updateTest(1, 'success', 'Camera and microphone access granted', mediaCheck);
        } else {
          updateTest(1, 'error', mediaCheck.error || 'Media access denied', mediaCheck);
        }
      } catch (error) {
        updateTest(1, 'error', `Media test failed: ${error}`, error);
      }

      // Test 3: Session Creation
      console.log('🧪 Running session creation test...');
      try {
        const session = await liveKit.createSession(['strategic', 'product'], 'video');
        setCurrentSession(session);
        updateTest(2, 'success', `Session created: ${session.id}`, session);
      } catch (error) {
        updateTest(2, 'error', `Session creation failed: ${error}`, error);
        setIsRunning(false);
        return;
      }

      // Test 4: Room Connection
      if (currentSession) {
        console.log('🧪 Running room connection test...');
        try {
          const room = await liveKit.connectToRoom(currentSession);
          updateTest(3, 'success', 'Successfully connected to room', room);
        } catch (error) {
          updateTest(3, 'error', `Room connection failed: ${error}`, error);
        }
      }

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      toast.error('Test suite failed');
    } finally {
      setIsRunning(false);
    }
  };

  const testLiveSession = async () => {
    try {
      toast.info('Starting live session test...');
      const session = await aiOrchestrator.startLiveKitSession(['strategic', 'product'], 'video');
      toast.success('Live session test completed!');
      console.log('🎉 Live session test result:', session);
    } catch (error) {
      toast.error('Live session test failed');
      console.error('❌ Live session test failed:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '❓';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🎥 LiveKit Debug & Test Page
          </h1>
          <p className="text-gray-600 mb-6">
            This page helps diagnose and test LiveKit functionality in the Ultimate Co-founder application.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h2 className="text-xl font-semibold mb-4">🧪 Diagnostic Tests</h2>
              <div className="space-y-3">
                {tests.map((test, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${getStatusColor(test.status)}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">
                        {getStatusIcon(test.status)} {test.name}
                      </span>
                    </div>
                    <p className="text-sm mt-1">{test.message}</p>
                    {test.details && (
                      <details className="mt-2">
                        <summary className="text-xs cursor-pointer">View Details</summary>
                        <pre className="text-xs mt-1 p-2 bg-gray-100 rounded overflow-auto">
                          {JSON.stringify(test.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-4">🎮 Test Controls</h2>
              <div className="space-y-4">
                <button
                  onClick={runTests}
                  disabled={isRunning}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRunning ? '🔄 Running Tests...' : '🚀 Run All Tests'}
                </button>

                <button
                  onClick={testLiveSession}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
                >
                  🎬 Test Live Session (Full Flow)
                </button>

                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
                >
                  🔄 Reset Tests
                </button>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">💡 Troubleshooting Tips</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Make sure you're logged in to the application</li>
                  <li>• Allow camera/microphone permissions when prompted</li>
                  <li>• Try refreshing the page if tests fail</li>
                  <li>• Check browser console for detailed error messages</li>
                  <li>• Disable ad blockers that might block WebRTC</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold mb-4">📊 System Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-gray-50 p-3 rounded">
                <strong>Browser:</strong> {navigator.userAgent.split(' ')[0]}
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <strong>Platform:</strong> {navigator.platform}
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <strong>URL:</strong> {window.location.href}
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">⚠️ Important Notes</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• This is a development/testing page - not for production use</li>
              <li>• LiveKit sessions in development mode use mock credentials</li>
              <li>• Real LiveKit functionality requires proper server configuration</li>
              <li>• Check the browser console for detailed logs during testing</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
