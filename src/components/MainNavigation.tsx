import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Zap, 
  User, 
  LogOut, 
  Menu, 
  X, 
  ChevronDown,
  Users,
  Database,
  FileText,
  Settings,
  CreditCard,
  Building2,
  Globe,
  HelpCircle,
  Mail,
  Briefcase,
  Rocket,
  TrendingUp,
  Presentation,
  Edit,
  Video
} from 'lucide-react';
import { useAuth } from './AuthProvider';
import { ThemeToggle } from './ThemeToggle';

export const MainNavigation: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const toggleDropdown = (dropdown: string) => {
    setOpenDropdown(openDropdown === dropdown ? null : dropdown);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setOpenDropdown(null);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center gap-2" onClick={closeMenu}>
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">AI Co-founder</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-1">
            {/* Features Dropdown */}
            <div className="relative">
              <button 
                className={`px-3 py-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-1 ${
                  openDropdown === 'features' ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : ''
                }`}
                onClick={() => toggleDropdown('features')}
              >
                Features
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'features' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'features' && (
                <div className="absolute left-0 mt-2 w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 p-2 z-50">
                  <Link to="/ai-cofounders" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Users size={16} />
                    <span>AI Co-founders</span>
                  </Link>
                  <Link to="/data-room" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Database size={16} />
                    <span>Data Room</span>
                  </Link>
                  <Link to="/integrations" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Zap size={16} />
                    <span>Integrations</span>
                  </Link>
                  <Link to="/documents" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <FileText size={16} />
                    <span>Documents</span>
                  </Link>
                </div>
              )}
            </div>

            {/* Use Cases Dropdown */}
            <div className="relative">
              <button 
                className={`px-3 py-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-1 ${
                  openDropdown === 'usecases' ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : ''
                }`}
                onClick={() => toggleDropdown('usecases')}
              >
                Use Cases
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'usecases' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'usecases' && (
                <div className="absolute left-0 mt-2 w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 p-2 z-50">
                  <Link to="/use-cases/product-strategy" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Rocket size={16} />
                    <span>Product Strategy</span>
                  </Link>
                  <Link to="/use-cases/go-to-market" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <TrendingUp size={16} />
                    <span>Go-to-Market</span>
                  </Link>
                  <Link to="/use-cases/fundraising" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Presentation size={16} />
                    <span>Fundraising & Pitch</span>
                  </Link>
                  <Link to="/use-cases/operations" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Settings size={16} />
                    <span>Operations & Hiring</span>
                  </Link>
                </div>
              )}
            </div>

            {/* Pricing Link */}
            <Link 
              to="/pricing" 
              className={`px-3 py-2 rounded-lg transition-colors ${
                isActive('/pricing') 
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              onClick={closeMenu}
            >
              Pricing
            </Link>

            {/* Resources Dropdown */}
            <div className="relative">
              <button 
                className={`px-3 py-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-1 ${
                  openDropdown === 'resources' ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : ''
                }`}
                onClick={() => toggleDropdown('resources')}
              >
                Resources
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'resources' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'resources' && (
                <div className="absolute left-0 mt-2 w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 p-2 z-50">
                  <Link to="/documentation" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <FileText size={16} />
                    <span>Documentation</span>
                  </Link>
                  <Link to="/blog" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Edit size={16} />
                    <span>Blog</span>
                  </Link>
                  <Link to="/webinars" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Video size={16} />
                    <span>Webinars & Tutorials</span>
                  </Link>
                  <Link to="/community" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Users size={16} />
                    <span>Community</span>
                  </Link>
                </div>
              )}
            </div>

            {/* Company Dropdown */}
            <div className="relative">
              <button 
                className={`px-3 py-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-1 ${
                  openDropdown === 'company' ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white' : ''
                }`}
                onClick={() => toggleDropdown('company')}
              >
                Company
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'company' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'company' && (
                <div className="absolute left-0 mt-2 w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 p-2 z-50">
                  <Link to="/about" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Building2 size={16} />
                    <span>About Us</span>
                  </Link>
                  <Link to="/careers" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Briefcase size={16} />
                    <span>Careers</span>
                  </Link>
                  <Link to="/contact" className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    <Mail size={16} />
                    <span>Contact</span>
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - User Menu & Theme Toggle */}
          <div className="flex items-center gap-4">
            <ThemeToggle />
            
            {isAuthenticated ? (
              <div className="flex items-center gap-3">
                <div className="hidden md:flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <User className="w-4 h-4" />
                  {user?.name}
                </div>
                <Link to="/dashboard" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                  Dashboard
                </Link>
                <button
                  onClick={logout}
                  className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <button 
                onClick={() => window.location.href = '/auth'}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Sign In
              </button>
            )}

            {/* Mobile menu button */}
            <button
              className="md:hidden p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="px-4 py-3 space-y-1">
            <div className="py-2 border-b border-gray-200 dark:border-gray-700">
              <button 
                className="w-full flex items-center justify-between px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={() => toggleDropdown('mobile-features')}
              >
                <span className="font-medium">Features</span>
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'mobile-features' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'mobile-features' && (
                <div className="mt-2 pl-4 space-y-1">
                  <Link to="/ai-cofounders" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    AI Co-founders
                  </Link>
                  <Link to="/data-room" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Data Room
                  </Link>
                  <Link to="/integrations" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Integrations
                  </Link>
                  <Link to="/documents" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Documents
                  </Link>
                </div>
              )}
            </div>

            <div className="py-2 border-b border-gray-200 dark:border-gray-700">
              <button 
                className="w-full flex items-center justify-between px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={() => toggleDropdown('mobile-usecases')}
              >
                <span className="font-medium">Use Cases</span>
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'mobile-usecases' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'mobile-usecases' && (
                <div className="mt-2 pl-4 space-y-1">
                  <Link to="/use-cases/product-strategy" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Product Strategy
                  </Link>
                  <Link to="/use-cases/go-to-market" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Go-to-Market
                  </Link>
                  <Link to="/use-cases/fundraising" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Fundraising & Pitch
                  </Link>
                  <Link to="/use-cases/operations" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Operations & Hiring
                  </Link>
                </div>
              )}
            </div>

            <Link to="/pricing" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border-b border-gray-200 dark:border-gray-700" onClick={closeMenu}>
              Pricing
            </Link>

            <div className="py-2 border-b border-gray-200 dark:border-gray-700">
              <button 
                className="w-full flex items-center justify-between px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={() => toggleDropdown('mobile-resources')}
              >
                <span className="font-medium">Resources</span>
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'mobile-resources' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'mobile-resources' && (
                <div className="mt-2 pl-4 space-y-1">
                  <Link to="/documentation" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Documentation
                  </Link>
                  <Link to="/blog" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Blog
                  </Link>
                  <Link to="/webinars" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Webinars & Tutorials
                  </Link>
                  <Link to="/community" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Community
                  </Link>
                </div>
              )}
            </div>

            <div className="py-2">
              <button 
                className="w-full flex items-center justify-between px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                onClick={() => toggleDropdown('mobile-company')}
              >
                <span className="font-medium">Company</span>
                <ChevronDown size={16} className={`transition-transform ${openDropdown === 'mobile-company' ? 'rotate-180' : ''}`} />
              </button>
              {openDropdown === 'mobile-company' && (
                <div className="mt-2 pl-4 space-y-1">
                  <Link to="/about" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    About Us
                  </Link>
                  <Link to="/careers" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Careers
                  </Link>
                  <Link to="/contact" className="block px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" onClick={closeMenu}>
                    Contact
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};