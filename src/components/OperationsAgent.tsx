import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Settings, 
  Scale, 
  DollarSign, 
  Users, 
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Download,
  FileText,
  BarChart3,
  Shield,
  Briefcase,
  Calendar,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target
} from 'lucide-react';

interface LegalItem {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  timeline: string;
  status: 'pending' | 'in-progress' | 'completed';
}

interface FinancialProjection {
  year: number;
  revenue: number;
  cogs: number;
  opex: number;
  grossProfit: number;
  netProfit: number;
}

interface HiringPlan {
  month: number;
  role: string;
  department: string;
  salary: number;
  priority: 'critical' | 'important' | 'nice-to-have';
  description: string;
}

interface ProcessOptimization {
  category: 'development' | 'business' | 'operations';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  timeline: string;
}

interface VoiceSession {
  url: string;
  isActive: boolean;
  transcript: string[];
  duration: number;
}

export const OperationsAgent: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'intro' | 'voice' | 'legal' | 'financial' | 'hiring' | 'processes' | 'complete'>('intro');
  const [voiceSession, setVoiceSession] = useState<VoiceSession>({
    url: '',
    isActive: false,
    transcript: [],
    duration: 0
  });
  const [legalItems, setLegalItems] = useState<LegalItem[]>([]);
  const [financialModel, setFinancialModel] = useState<FinancialProjection[]>([]);
  const [hiringPlan, setHiringPlan] = useState<HiringPlan[]>([]);
  const [processOptimizations, setProcessOptimizations] = useState<ProcessOptimization[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const startVoiceSession = () => {
    setVoiceSession({
      url: 'wss://operations-cofounder.livekit.cloud',
      isActive: true,
      transcript: [
        "Operations Agent: Hello! I'm Jordan, your Operations Co-founder. I'll provide a comprehensive overview of the legal, financial, and operational foundations you'll need.",
        "Operations Agent: Let me walk you through the critical compliance requirements, financial planning, and team structure for your startup's success.",
        "Operations Agent: We'll cover incorporation, privacy policies, financial projections, hiring timelines, and process optimizations."
      ],
      duration: 0
    });
    setCurrentStep('voice');

    // Simulate 2-minute voice session
    const timer = setInterval(() => {
      setVoiceSession(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    setTimeout(() => {
      clearInterval(timer);
      endVoiceSession();
    }, 120000); // 2 minutes
  };

  const endVoiceSession = () => {
    setVoiceSession(prev => ({ ...prev, isActive: false }));
    setCurrentStep('legal');
    generateLegalItems();
  };

  const generateLegalItems = async () => {
    setIsProcessing(true);
    await new Promise(resolve => setTimeout(resolve, 1500));

    const mockLegalItems: LegalItem[] = [
      {
        id: 'incorporation',
        title: 'Business Incorporation',
        description: 'Register as Delaware C-Corp for investor readiness and tax benefits',
        priority: 'high',
        timeline: 'Week 1-2',
        status: 'pending'
      },
      {
        id: 'privacy',
        title: 'Privacy Policy & GDPR Compliance',
        description: 'Comprehensive privacy policy covering data collection, processing, and user rights',
        priority: 'high',
        timeline: 'Week 2-3',
        status: 'pending'
      },
      {
        id: 'terms',
        title: 'Terms of Service',
        description: 'User agreement covering service usage, limitations, and liability',
        priority: 'high',
        timeline: 'Week 2-3',
        status: 'pending'
      },
      {
        id: 'security',
        title: 'Data Security Audit',
        description: 'SOC 2 Type II compliance and security framework implementation',
        priority: 'medium',
        timeline: 'Month 2-3',
        status: 'pending'
      },
      {
        id: 'ip',
        title: 'IP Assignment Agreements',
        description: 'Intellectual property assignment for all team members and contractors',
        priority: 'high',
        timeline: 'Week 1',
        status: 'pending'
      }
    ];

    setLegalItems(mockLegalItems);
    setIsProcessing(false);
    generateFinancialModel();
  };

  const generateFinancialModel = async () => {
    setIsProcessing(true);
    setCurrentStep('financial');
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockFinancialModel: FinancialProjection[] = [
      {
        year: 1,
        revenue: 250000,
        cogs: 75000,
        opex: 180000,
        grossProfit: 175000,
        netProfit: -5000
      },
      {
        year: 2,
        revenue: 850000,
        cogs: 255000,
        opex: 420000,
        grossProfit: 595000,
        netProfit: 175000
      },
      {
        year: 3,
        revenue: 2100000,
        cogs: 630000,
        opex: 890000,
        grossProfit: 1470000,
        netProfit: 580000
      }
    ];

    setFinancialModel(mockFinancialModel);
    setIsProcessing(false);
    generateHiringPlan();
  };

  const generateHiringPlan = async () => {
    setIsProcessing(true);
    setCurrentStep('hiring');
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockHiringPlan: HiringPlan[] = [
      {
        month: 1,
        role: 'Senior Backend Engineer',
        department: 'Engineering',
        salary: 140000,
        priority: 'critical',
        description: 'Lead API development and system architecture'
      },
      {
        month: 2,
        role: 'UI/UX Designer',
        department: 'Product',
        salary: 95000,
        priority: 'critical',
        description: 'Design user interfaces and optimize user experience'
      },
      {
        month: 3,
        role: 'Frontend Engineer',
        department: 'Engineering',
        salary: 120000,
        priority: 'important',
        description: 'Build responsive web applications and user interfaces'
      },
      {
        month: 4,
        role: 'DevOps Engineer',
        department: 'Engineering',
        salary: 130000,
        priority: 'important',
        description: 'Manage infrastructure, CI/CD, and deployment automation'
      },
      {
        month: 5,
        role: 'Sales Development Rep',
        department: 'Sales',
        salary: 65000,
        priority: 'important',
        description: 'Generate leads and qualify prospects for enterprise sales'
      },
      {
        month: 6,
        role: 'Customer Success Manager',
        department: 'Customer Success',
        salary: 85000,
        priority: 'nice-to-have',
        description: 'Ensure customer satisfaction and drive retention'
      }
    ];

    setHiringPlan(mockHiringPlan);
    setIsProcessing(false);
    generateProcessOptimizations();
  };

  const generateProcessOptimizations = async () => {
    setIsProcessing(true);
    setCurrentStep('processes');
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockProcessOptimizations: ProcessOptimization[] = [
      {
        category: 'development',
        title: 'CI/CD Pipeline Automation',
        description: 'Implement automated testing, building, and deployment pipeline with GitHub Actions',
        impact: 'high',
        effort: 'medium',
        timeline: 'Week 2-3'
      },
      {
        category: 'business',
        title: 'Weekly Sprint Reviews',
        description: 'Establish agile development process with regular sprint planning and retrospectives',
        impact: 'medium',
        effort: 'low',
        timeline: 'Week 1'
      },
      {
        category: 'operations',
        title: 'Automated Monitoring & Alerts',
        description: 'Set up comprehensive application monitoring with Datadog and PagerDuty integration',
        impact: 'high',
        effort: 'medium',
        timeline: 'Week 3-4'
      }
    ];

    setProcessOptimizations(mockProcessOptimizations);
    setIsProcessing(false);
    setCurrentStep('complete');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300';
      case 'medium':
      case 'important':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
      case 'nice-to-have':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const generateReport = () => {
    return `# Operations Strategy Report

## Executive Summary
Comprehensive operational foundation including legal compliance, financial projections, hiring strategy, and process optimizations for startup success.

## Legal & Compliance Checklist

${legalItems.map((item, index) => `
${index + 1}. **${item.title}** (${item.priority.toUpperCase()} priority)
   - **Timeline:** ${item.timeline}
   - **Description:** ${item.description}
   - **Status:** ${item.status}
`).join('')}

## 3-Year Financial Model

| Year | Revenue | COGS | OpEx | Gross Profit | Net Profit |
|------|---------|------|------|--------------|------------|
${financialModel.map(year => 
  `| ${year.year} | ${formatCurrency(year.revenue)} | ${formatCurrency(year.cogs)} | ${formatCurrency(year.opex)} | ${formatCurrency(year.grossProfit)} | ${formatCurrency(year.netProfit)} |`
).join('\n')}

### Key Financial Metrics
- **Year 1:** Focus on product-market fit, expect initial losses
- **Year 2:** Revenue growth acceleration, path to profitability
- **Year 3:** Scaled operations with strong profit margins

## 6-Month Hiring Plan

${hiringPlan.map(hire => `
**Month ${hire.month}: ${hire.role}** (${hire.department})
- **Salary:** ${formatCurrency(hire.salary)}
- **Priority:** ${hire.priority}
- **Role:** ${hire.description}
`).join('')}

### Total 6-Month Hiring Budget: ${formatCurrency(hiringPlan.reduce((total, hire) => total + hire.salary, 0))}

## Process Optimizations

${processOptimizations.map((process, index) => `
${index + 1}. **${process.title}** (${process.category.toUpperCase()})
   - **Impact:** ${process.impact} | **Effort:** ${process.effort}
   - **Timeline:** ${process.timeline}
   - **Description:** ${process.description}
`).join('')}

---
*Generated by Operations Co-founder Agent | Voice Session Duration: ${Math.floor(voiceSession.duration / 60)}:${(voiceSession.duration % 60).toString().padStart(2, '0')}*`;
  };

  const downloadReport = () => {
    const report = generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'operations-strategy-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Settings className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Operations Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Legal, financial, and operational foundation setup
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-2 mb-8 overflow-x-auto">
        {[
          { id: 'intro', label: 'Introduction', icon: Briefcase },
          { id: 'voice', label: 'Voice Brief', icon: Mic },
          { id: 'legal', label: 'Legal', icon: Scale },
          { id: 'financial', label: 'Financial', icon: DollarSign },
          { id: 'hiring', label: 'Hiring', icon: Users },
          { id: 'processes', label: 'Processes', icon: Settings },
          { id: 'complete', label: 'Report', icon: FileText }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs ${
              currentStep === step.id 
                ? 'bg-green-600 text-white' 
                : index < ['intro', 'voice', 'legal', 'financial', 'hiring', 'processes', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={12} />
            </div>
            <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300 hidden sm:inline">
              {step.label}
            </span>
            {index < 6 && (
              <div className={`w-4 h-0.5 mx-2 ${
                index < ['intro', 'voice', 'legal', 'financial', 'hiring', 'processes', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Let's Build Your Operational Foundation
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll provide a comprehensive 2-minute voice briefing on legal compliance, then generate detailed plans for legal setup, financial projections, hiring strategy, and process optimizations.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-center">
                <Scale className="w-8 h-8 text-red-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Legal Setup</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">5 critical compliance items</p>
              </div>
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
                <DollarSign className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Financial Model</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">3-year projections</p>
              </div>
              <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-center">
                <Users className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Hiring Plan</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">6-month roadmap</p>
              </div>
              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                <Settings className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <h3 className="font-medium text-gray-900 dark:text-white">Processes</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">3 key optimizations</p>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={startVoiceSession}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-semibold text-lg transition-all"
            >
              <Phone size={20} />
              Start LiveKit Voice Briefing
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'voice' && (
          <motion.div
            key="voice"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Mic className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Live Voice Briefing Active
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(voiceSession.duration / 60)}:{(voiceSession.duration % 60).toString().padStart(2, '0')} / 2:00
              </p>
            </div>

            {/* Voice Visualizer */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6">
              <div className="flex items-center justify-center space-x-1 h-16">
                {[...Array(15)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-1 bg-green-400 rounded-full"
                    animate={{
                      height: [4, Math.random() * 40 + 10, 4],
                    }}
                    transition={{
                      duration: 0.6,
                      repeat: Infinity,
                      delay: i * 0.1,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Live Transcript */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 max-h-40 overflow-y-auto">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Live Transcript</h3>
              <div className="space-y-2">
                {voiceSession.transcript.map((message, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-400">
                    {message}
                  </p>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={endVoiceSession}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
              >
                <PhoneOff size={16} />
                End Briefing
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <MicOff size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'legal' && (
          <motion.div
            key="legal"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Analyzing Legal Requirements...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Identifying critical compliance and legal setup items
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Legal & Compliance Checklist
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    5 must-have legal steps for startup foundation
                  </p>
                </div>

                <div className="space-y-4">
                  {legalItems.map((item, index) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-4">
                          <div className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm">
                            {index + 1}
                          </div>
                          <div>
                            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                              {item.title}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-3">
                              {item.description}
                            </p>
                            <div className="flex items-center gap-3">
                              <span className={`px-3 py-1 text-xs font-medium rounded-full ${getPriorityColor(item.priority)}`}>
                                {item.priority.toUpperCase()} Priority
                              </span>
                              <span className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                                <Clock size={14} />
                                {item.timeline}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {item.status === 'completed' ? (
                            <CheckCircle className="w-6 h-6 text-green-500" />
                          ) : item.status === 'in-progress' ? (
                            <Clock className="w-6 h-6 text-yellow-500" />
                          ) : (
                            <AlertTriangle className="w-6 h-6 text-red-500" />
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'financial' && (
          <motion.div
            key="financial"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Building Financial Model...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating 3-year revenue and expense projections
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    3-Year Financial Model
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Revenue, COGS, and OpEx projections
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Year</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">Revenue</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">COGS</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">OpEx</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">Gross Profit</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">Net Profit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {financialModel.map((year, index) => (
                        <motion.tr
                          key={year.year}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="border-b border-gray-100 dark:border-gray-700"
                        >
                          <td className="py-4 px-4 font-medium text-gray-900 dark:text-white">
                            Year {year.year}
                          </td>
                          <td className="py-4 px-4 text-right text-gray-900 dark:text-white">
                            {formatCurrency(year.revenue)}
                          </td>
                          <td className="py-4 px-4 text-right text-gray-900 dark:text-white">
                            {formatCurrency(year.cogs)}
                          </td>
                          <td className="py-4 px-4 text-right text-gray-900 dark:text-white">
                            {formatCurrency(year.opex)}
                          </td>
                          <td className="py-4 px-4 text-right text-gray-900 dark:text-white">
                            {formatCurrency(year.grossProfit)}
                          </td>
                          <td className={`py-4 px-4 text-right font-medium ${
                            year.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {formatCurrency(year.netProfit)}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Financial Insights */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">Year 1 Focus</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Product-market fit and initial customer acquisition. Expect initial losses as you invest in growth.
                    </p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 dark:text-green-200 mb-2">Year 2 Growth</h4>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Revenue acceleration and path to profitability with optimized operations and scaling.
                    </p>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-purple-900 dark:text-purple-200 mb-2">Year 3 Scale</h4>
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      Strong profit margins with established market position and operational efficiency.
                    </p>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'hiring' && (
          <motion.div
            key="hiring"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Creating Hiring Plan...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Developing 6-month team building strategy
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    6-Month Hiring Plan
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Strategic team building roadmap with budget allocation
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {hiringPlan.map((hire, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold">
                            M{hire.month}
                          </div>
                          <div>
                            <h3 className="font-bold text-gray-900 dark:text-white">
                              {hire.role}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {hire.department}
                            </p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(hire.priority)}`}>
                          {hire.priority}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        {hire.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-gray-900 dark:text-white">
                          {formatCurrency(hire.salary)}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          /year
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Hiring Summary */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Hiring Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {hiringPlan.length}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Hires</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(hiringPlan.reduce((total, hire) => total + hire.salary, 0))}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Total Budget</p>
                    </div>
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {hiringPlan.filter(h => h.priority === 'critical').length}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Critical Roles</p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'processes' && (
          <motion.div
            key="processes"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Optimizing Processes...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Identifying key development and business optimizations
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Process Optimizations
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    3 key improvements for development and business operations
                  </p>
                </div>

                <div className="space-y-4">
                  {processOptimizations.map((process, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-4">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                            process.category === 'development' ? 'bg-blue-500' :
                            process.category === 'business' ? 'bg-purple-500' : 'bg-green-500'
                          }`}>
                            {process.category === 'development' ? '⚡' :
                             process.category === 'business' ? '📊' : '⚙️'}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                                {process.title}
                              </h3>
                              <span className="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full uppercase">
                                {process.category}
                              </span>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 mb-3">
                              {process.description}
                            </p>
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-500 dark:text-gray-400">Impact:</span>
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(process.impact)}`}>
                                  {process.impact.toUpperCase()}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-500 dark:text-gray-400">Effort:</span>
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                  process.effort === 'low' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                                  process.effort === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300' :
                                  'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                                }`}>
                                  {process.effort.toUpperCase()}
                                </span>
                              </div>
                              <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                                <Calendar size={14} />
                                {process.timeline}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'complete' && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Operations Foundation Complete
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your comprehensive operational strategy is ready
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Scale className="w-8 h-8 text-red-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{legalItems.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Legal Items</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <DollarSign className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">3</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Year Model</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Users className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{hiringPlan.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Hires Planned</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Settings className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{processOptimizations.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Optimizations</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Operations Report
              </motion.button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setLegalItems([]);
                  setFinancialModel([]);
                  setHiringPlan([]);
                  setProcessOptimizations([]);
                  setVoiceSession(prev => ({ ...prev, transcript: [], duration: 0 }));
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                New Analysis
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};