import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Users,
  Code2,
  Zap,
  Video,
  Settings,
  Target,
  Rocket,
  Download,
  FileText,
  Play,
  Pause,
  RotateCcw,
  TrendingUp,
  GitBranch,
  TestTube,
  Globe,
  Handshake
} from 'lucide-react';

interface TimelineTask {
  id: string;
  title: string;
  description: string;
  category: 'development' | 'testing' | 'integration' | 'deployment' | 'business';
  priority: 'high' | 'medium' | 'low';
  estimatedHours: number;
  dependencies: string[];
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  assignee: string;
  dueDate: string;
}

interface WeekPlan {
  weekNumber: number;
  startDate: string;
  endDate: string;
  theme: string;
  description: string;
  objectives: string[];
  tasks: TimelineTask[];
  milestones: string[];
  risks: string[];
}

interface ProjectMetrics {
  totalTasks: number;
  completedTasks: number;
  totalHours: number;
  completedHours: number;
  criticalPath: string[];
  teamUtilization: number;
}

export const TimelineAgent: React.FC = () => {
  const [currentWeek, setCurrentWeek] = useState(1);
  const [isSimulating, setIsSimulating] = useState(false);
  const [simulationSpeed, setSimulationSpeed] = useState(1);
  const [weekPlans, setWeekPlans] = useState<WeekPlan[]>([]);
  const [projectMetrics, setProjectMetrics] = useState<ProjectMetrics | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date('2025-06-25'));

  // Initialize timeline data
  useEffect(() => {
    const mockWeekPlans: WeekPlan[] = [
      {
        weekNumber: 1,
        startDate: '2025-06-25',
        endDate: '2025-07-01',
        theme: 'Foundation & Architecture',
        description: 'Establish core infrastructure and basic agent framework',
        objectives: [
          'Set up CrewAI + LangChain orchestrator foundation',
          'Implement Strategic & Product agents with basic functionality',
          'Create BoltIntegrationAgent proof of concept',
          'Establish development environment and CI/CD pipeline'
        ],
        tasks: [
          {
            id: 'w1-t1',
            title: 'Build boilerplate CrewAI + LangChain orchestrator',
            description: 'Create the core orchestration system that coordinates all AI agents',
            category: 'development',
            priority: 'high',
            estimatedHours: 16,
            dependencies: [],
            status: 'pending',
            assignee: 'Lead Developer',
            dueDate: '2025-06-27'
          },
          {
            id: 'w1-t2',
            title: 'Spin up Strategic & Product agents',
            description: 'Implement basic Strategic and Product co-founder agents with core capabilities',
            category: 'development',
            priority: 'high',
            estimatedHours: 20,
            dependencies: ['w1-t1'],
            status: 'pending',
            assignee: 'AI Engineer',
            dueDate: '2025-06-29'
          },
          {
            id: 'w1-t3',
            title: 'Smoke-test agent communication',
            description: 'Verify agents can communicate and coordinate effectively',
            category: 'testing',
            priority: 'medium',
            estimatedHours: 8,
            dependencies: ['w1-t2'],
            status: 'pending',
            assignee: 'QA Engineer',
            dueDate: '2025-06-30'
          },
          {
            id: 'w1-t4',
            title: 'Setup BoltIntegrationAgent POC',
            description: 'Create proof of concept for Bolt.new integration with screen automation',
            category: 'integration',
            priority: 'medium',
            estimatedHours: 12,
            dependencies: ['w1-t1'],
            status: 'pending',
            assignee: 'Integration Specialist',
            dueDate: '2025-07-01'
          }
        ],
        milestones: [
          'Core orchestrator operational',
          'First two agents functional',
          'Basic integration pipeline established'
        ],
        risks: [
          'CrewAI learning curve may slow initial development',
          'Agent coordination complexity higher than expected'
        ]
      },
      {
        weekNumber: 2,
        startDate: '2025-07-02',
        endDate: '2025-07-08',
        theme: 'Agent Completion & Testing',
        description: 'Complete remaining agents and establish comprehensive testing framework',
        objectives: [
          'Implement Technical & Operations agents',
          'Create comprehensive integration test suite',
          'Begin LiveKit credential setup and integration',
          'Establish monitoring and logging systems'
        ],
        tasks: [
          {
            id: 'w2-t1',
            title: 'Finalize Technical & Operations agents',
            description: 'Complete development of Technical and Operations co-founder agents',
            category: 'development',
            priority: 'high',
            estimatedHours: 24,
            dependencies: ['w1-t2'],
            status: 'pending',
            assignee: 'AI Engineer',
            dueDate: '2025-07-05'
          },
          {
            id: 'w2-t2',
            title: 'Write integration tests for each agent',
            description: 'Create comprehensive test suite covering all agent interactions',
            category: 'testing',
            priority: 'high',
            estimatedHours: 16,
            dependencies: ['w2-t1'],
            status: 'pending',
            assignee: 'QA Engineer',
            dueDate: '2025-07-07'
          },
          {
            id: 'w2-t3',
            title: 'Begin LiveKit credential setup',
            description: 'Configure LiveKit infrastructure for video/voice capabilities',
            category: 'integration',
            priority: 'medium',
            estimatedHours: 10,
            dependencies: [],
            status: 'pending',
            assignee: 'DevOps Engineer',
            dueDate: '2025-07-08'
          },
          {
            id: 'w2-t4',
            title: 'Implement monitoring & logging',
            description: 'Set up comprehensive monitoring for agent performance and system health',
            category: 'development',
            priority: 'medium',
            estimatedHours: 12,
            dependencies: ['w2-t1'],
            status: 'pending',
            assignee: 'DevOps Engineer',
            dueDate: '2025-07-08'
          }
        ],
        milestones: [
          'All 5 agents operational',
          'Integration test suite complete',
          'LiveKit infrastructure ready'
        ],
        risks: [
          'LiveKit integration complexity',
          'Agent performance optimization challenges'
        ]
      },
      {
        weekNumber: 3,
        startDate: '2025-07-09',
        endDate: '2025-07-15',
        theme: 'UI/UX & Real-time Integration',
        description: 'Build user interface and integrate real-time communication features',
        objectives: [
          'Develop comprehensive UI workflows',
          'Implement WebSocket progress tracking',
          'Integrate LiveKit sessions into orchestrator',
          'Conduct end-to-end system testing'
        ],
        tasks: [
          {
            id: 'w3-t1',
            title: 'Develop UI workflows + WebSocket progress tracker',
            description: 'Create responsive UI with real-time progress tracking for all agent activities',
            category: 'development',
            priority: 'high',
            estimatedHours: 28,
            dependencies: ['w2-t1'],
            status: 'pending',
            assignee: 'Frontend Developer',
            dueDate: '2025-07-12'
          },
          {
            id: 'w3-t2',
            title: 'Integrate LiveKit sessions into orchestrator',
            description: 'Connect LiveKit video/voice capabilities with agent orchestration system',
            category: 'integration',
            priority: 'high',
            estimatedHours: 20,
            dependencies: ['w2-t3', 'w3-t1'],
            status: 'pending',
            assignee: 'Integration Specialist',
            dueDate: '2025-07-14'
          },
          {
            id: 'w3-t3',
            title: 'Run end-to-end demo',
            description: 'Execute complete workflow from user input to final report generation',
            category: 'testing',
            priority: 'high',
            estimatedHours: 12,
            dependencies: ['w3-t2'],
            status: 'pending',
            assignee: 'QA Engineer',
            dueDate: '2025-07-15'
          },
          {
            id: 'w3-t4',
            title: 'Performance optimization',
            description: 'Optimize agent response times and system performance',
            category: 'development',
            priority: 'medium',
            estimatedHours: 16,
            dependencies: ['w3-t2'],
            status: 'pending',
            assignee: 'Backend Developer',
            dueDate: '2025-07-15'
          }
        ],
        milestones: [
          'Complete UI/UX implementation',
          'LiveKit fully integrated',
          'End-to-end demo successful'
        ],
        risks: [
          'Real-time synchronization challenges',
          'Performance bottlenecks in agent coordination'
        ]
      },
      {
        weekNumber: 4,
        startDate: '2025-07-16',
        endDate: '2025-07-23',
        theme: 'Polish, Demo & Partnerships',
        description: 'Finalize product for demo and establish strategic partnerships',
        objectives: [
          'Create polished demo and conduct user testing',
          'Develop MonetizationAgent & PitchAgent presentations',
          'Initiate partnership outreach to StackBlitz',
          'Prepare for public launch and investor presentations'
        ],
        tasks: [
          {
            id: 'w4-t1',
            title: 'Polished demo + user testing',
            description: 'Create production-ready demo and conduct user testing sessions',
            category: 'testing',
            priority: 'high',
            estimatedHours: 20,
            dependencies: ['w3-t3'],
            status: 'pending',
            assignee: 'Product Manager',
            dueDate: '2025-07-19'
          },
          {
            id: 'w4-t2',
            title: 'MonetizationAgent & PitchAgent presentations',
            description: 'Develop comprehensive monetization strategy and pitch materials',
            category: 'business',
            priority: 'high',
            estimatedHours: 16,
            dependencies: ['w4-t1'],
            status: 'pending',
            assignee: 'Business Development',
            dueDate: '2025-07-21'
          },
          {
            id: 'w4-t3',
            title: 'Partnership outreach to StackBlitz',
            description: 'Initiate strategic partnership discussions with StackBlitz team',
            category: 'business',
            priority: 'medium',
            estimatedHours: 12,
            dependencies: ['w4-t2'],
            status: 'pending',
            assignee: 'CEO',
            dueDate: '2025-07-23'
          },
          {
            id: 'w4-t4',
            title: 'Launch preparation',
            description: 'Prepare marketing materials, documentation, and launch strategy',
            category: 'business',
            priority: 'medium',
            estimatedHours: 18,
            dependencies: ['w4-t1'],
            status: 'pending',
            assignee: 'Marketing Team',
            dueDate: '2025-07-23'
          }
        ],
        milestones: [
          'Production-ready demo complete',
          'Monetization strategy finalized',
          'StackBlitz partnership initiated',
          'Launch readiness achieved'
        ],
        risks: [
          'User feedback may require significant changes',
          'Partnership negotiations may extend timeline'
        ]
      }
    ];

    setWeekPlans(mockWeekPlans);
    calculateProjectMetrics(mockWeekPlans);
  }, []);

  const calculateProjectMetrics = (plans: WeekPlan[]) => {
    const allTasks = plans.flatMap(plan => plan.tasks);
    const completedTasks = allTasks.filter(task => task.status === 'completed');
    const totalHours = allTasks.reduce((sum, task) => sum + task.estimatedHours, 0);
    const completedHours = completedTasks.reduce((sum, task) => sum + task.estimatedHours, 0);

    const metrics: ProjectMetrics = {
      totalTasks: allTasks.length,
      completedTasks: completedTasks.length,
      totalHours,
      completedHours,
      criticalPath: ['w1-t1', 'w1-t2', 'w2-t1', 'w3-t1', 'w3-t2', 'w4-t1'],
      teamUtilization: 85
    };

    setProjectMetrics(metrics);
  };

  const startSimulation = () => {
    setIsSimulating(true);
    simulateProgress();
  };

  const simulateProgress = () => {
    const interval = setInterval(() => {
      setWeekPlans(prev => prev.map(week => ({
        ...week,
        tasks: week.tasks.map(task => {
          if (task.status === 'pending' && Math.random() > 0.7) {
            return { ...task, status: 'in-progress' };
          }
          if (task.status === 'in-progress' && Math.random() > 0.8) {
            return { ...task, status: 'completed' };
          }
          return task;
        })
      })));

      // Advance current date
      setCurrentDate(prev => new Date(prev.getTime() + (24 * 60 * 60 * 1000 * simulationSpeed)));
    }, 1000 / simulationSpeed);

    // Stop simulation after demo period
    setTimeout(() => {
      clearInterval(interval);
      setIsSimulating(false);
    }, 30000);
  };

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in-progress':
        return <Clock className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'blocked':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-gray-300 rounded-full" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'development':
        return <Code2 className="w-4 h-4 text-blue-500" />;
      case 'testing':
        return <TestTube className="w-4 h-4 text-green-500" />;
      case 'integration':
        return <Zap className="w-4 h-4 text-purple-500" />;
      case 'deployment':
        return <Rocket className="w-4 h-4 text-orange-500" />;
      case 'business':
        return <TrendingUp className="w-4 h-4 text-pink-500" />;
      default:
        return <Settings className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'medium':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300';
    }
  };

  const generateTimelineReport = () => {
    return `# Ultimate Startup Co-founder - 4-Week Development Timeline

**Project Start Date:** June 25, 2025  
**Target Completion:** July 23, 2025  
**Total Duration:** 4 weeks (28 days)

## Executive Summary

This timeline outlines the comprehensive 4-week development plan for building the Ultimate Startup Co-founder platform, featuring 5 specialized AI agents with LiveKit integration and Bolt.new deployment capabilities.

## Project Metrics

- **Total Tasks:** ${projectMetrics?.totalTasks || 0}
- **Estimated Hours:** ${projectMetrics?.totalHours || 0}
- **Team Utilization:** ${projectMetrics?.teamUtilization || 0}%
- **Critical Path Length:** ${projectMetrics?.criticalPath.length || 0} tasks

## Weekly Breakdown

${weekPlans.map(week => `
### Week ${week.weekNumber}: ${week.theme}
**Dates:** ${week.startDate} to ${week.endDate}

**Description:** ${week.description}

**Objectives:**
${week.objectives.map(obj => `- ${obj}`).join('\n')}

**Tasks:**
${week.tasks.map(task => `
- **${task.title}** (${task.priority} priority)
  - **Category:** ${task.category}
  - **Estimated Hours:** ${task.estimatedHours}
  - **Assignee:** ${task.assignee}
  - **Due Date:** ${task.dueDate}
  - **Description:** ${task.description}
`).join('')}

**Milestones:**
${week.milestones.map(milestone => `- ${milestone}`).join('\n')}

**Risks:**
${week.risks.map(risk => `- ${risk}`).join('\n')}
`).join('')}

## Critical Path Analysis

The critical path consists of the following sequential tasks:
${projectMetrics?.criticalPath.map((taskId, index) => {
  const task = weekPlans.flatMap(w => w.tasks).find(t => t.id === taskId);
  return `${index + 1}. ${task?.title || taskId} (${task?.estimatedHours || 0}h)`;
}).join('\n')}

## Resource Allocation

- **Lead Developer:** Core orchestrator and architecture
- **AI Engineer:** Agent development and coordination
- **Frontend Developer:** UI/UX implementation
- **Backend Developer:** API and performance optimization
- **Integration Specialist:** LiveKit and Bolt.new integration
- **DevOps Engineer:** Infrastructure and monitoring
- **QA Engineer:** Testing and quality assurance
- **Product Manager:** User testing and requirements
- **Business Development:** Monetization and partnerships

## Success Criteria

1. **Week 1:** Core orchestrator operational with 2 functional agents
2. **Week 2:** All 5 agents complete with comprehensive testing
3. **Week 3:** Full UI/UX with LiveKit integration and end-to-end demo
4. **Week 4:** Production-ready platform with partnership pipeline

## Risk Mitigation

- **Technical Risks:** Daily standups and code reviews
- **Integration Risks:** Early POC development and testing
- **Timeline Risks:** Parallel development tracks and buffer time
- **Quality Risks:** Continuous testing and user feedback loops

---
*Generated by Timeline Agent | Current Date: ${currentDate.toLocaleDateString()}*`;
  };

  const downloadTimeline = () => {
    const report = generateTimelineReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ultimate-cofounder-timeline.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Calendar className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Timeline Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          4-Week Development Plan | June 25 - July 23, 2025
        </p>
      </div>

      {/* Project Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Project Overview</h2>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Date</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {currentDate.toLocaleDateString()}
              </p>
            </div>
            <button
              onClick={isSimulating ? () => setIsSimulating(false) : startSimulation}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                isSimulating
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-indigo-500 hover:bg-indigo-600 text-white'
              }`}
            >
              {isSimulating ? <Pause size={16} /> : <Play size={16} />}
              {isSimulating ? 'Pause Simulation' : 'Start Simulation'}
            </button>
          </div>
        </div>

        {/* Project Metrics */}
        {projectMetrics && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
              <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                {projectMetrics.totalTasks}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Tasks</p>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {projectMetrics.totalHours}h
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Estimated Hours</p>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {projectMetrics.teamUtilization}%
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Team Utilization</p>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {Math.round((projectMetrics.completedTasks / projectMetrics.totalTasks) * 100)}%
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Progress</p>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Overall Progress</span>
            <span>
              {projectMetrics?.completedTasks || 0} / {projectMetrics?.totalTasks || 0} tasks
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-1000"
              style={{ 
                width: `${projectMetrics ? (projectMetrics.completedTasks / projectMetrics.totalTasks) * 100 : 0}%` 
              }}
            />
          </div>
        </div>
      </div>

      {/* Week Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white">Weekly Timeline</h3>
          <div className="flex items-center gap-2">
            {[1, 2, 3, 4].map((week) => (
              <button
                key={week}
                onClick={() => setCurrentWeek(week)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  currentWeek === week
                    ? 'bg-indigo-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                Week {week}
              </button>
            ))}
          </div>
        </div>

        {/* Week Overview */}
        {weekPlans[currentWeek - 1] && (
          <motion.div
            key={currentWeek}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="border-l-4 border-indigo-500 pl-4">
              <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {weekPlans[currentWeek - 1].theme}
              </h4>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                {weekPlans[currentWeek - 1].description}
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                <span>📅 {weekPlans[currentWeek - 1].startDate} - {weekPlans[currentWeek - 1].endDate}</span>
                <span>📋 {weekPlans[currentWeek - 1].tasks.length} tasks</span>
                <span>⏱️ {weekPlans[currentWeek - 1].tasks.reduce((sum, task) => sum + task.estimatedHours, 0)}h estimated</span>
              </div>
            </div>

            {/* Objectives */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Week Objectives</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {weekPlans[currentWeek - 1].objectives.map((objective, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Target className="w-5 h-5 text-indigo-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{objective}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Tasks */}
            <div>
              <h5 className="font-medium text-gray-900 dark:text-white mb-3">Tasks</h5>
              <div className="space-y-3">
                {weekPlans[currentWeek - 1].tasks.map((task) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start gap-3">
                        {getTaskStatusIcon(task.status)}
                        <div>
                          <h6 className="font-medium text-gray-900 dark:text-white mb-1">
                            {task.title}
                          </h6>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {task.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              {getCategoryIcon(task.category)}
                              <span className="capitalize">{task.category}</span>
                            </div>
                            <span>⏱️ {task.estimatedHours}h</span>
                            <span>👤 {task.assignee}</span>
                            <span>📅 {task.dueDate}</span>
                          </div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                    </div>
                    
                    {task.dependencies.length > 0 && (
                      <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <GitBranch size={12} />
                        <span>Depends on: {task.dependencies.join(', ')}</span>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Milestones & Risks */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white mb-3">Milestones</h5>
                <div className="space-y-2">
                  {weekPlans[currentWeek - 1].milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      {milestone}
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white mb-3">Risks</h5>
                <div className="space-y-2">
                  {weekPlans[currentWeek - 1].risks.map((risk, index) => (
                    <div key={index} className="flex items-start gap-2 text-sm text-gray-700 dark:text-gray-300">
                      <AlertCircle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                      {risk}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Critical Path */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Critical Path Analysis</h3>
        <div className="space-y-3">
          {projectMetrics?.criticalPath.map((taskId, index) => {
            const task = weekPlans.flatMap(w => w.tasks).find(t => t.id === taskId);
            if (!task) return null;
            
            return (
              <div key={taskId} className="flex items-center gap-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <h6 className="font-medium text-gray-900 dark:text-white">{task.title}</h6>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {task.estimatedHours}h • {task.assignee} • Due {task.dueDate}
                  </p>
                </div>
                {getTaskStatusIcon(task.status)}
              </div>
            );
          })}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-4">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={downloadTimeline}
          className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-lg font-medium transition-all"
        >
          <Download size={16} />
          Download Timeline Report
        </motion.button>
        <button
          onClick={() => {
            setCurrentWeek(1);
            setCurrentDate(new Date('2025-06-25'));
            setIsSimulating(false);
            // Reset all tasks to pending
            setWeekPlans(prev => prev.map(week => ({
              ...week,
              tasks: week.tasks.map(task => ({ ...task, status: 'pending' }))
            })));
          }}
          className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
        >
          <RotateCcw size={16} />
        </button>
      </div>
    </div>
  );
};