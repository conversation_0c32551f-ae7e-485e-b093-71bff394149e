import React from 'react';
import { motion } from 'framer-motion';
import { useAgentStore } from '../store/agentStore';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Users,
  Settings,
  Monitor
} from 'lucide-react';

export const VideoCallPanel: React.FC = () => {
  const { agents, isVideoCallActive, toggleVideoCall } = useAgentStore();
  const [isCameraOn, setIsCameraOn] = React.useState(true);
  const [isMicOn, setIsMicOn] = React.useState(true);
  const [isScreenSharing, setIsScreenSharing] = React.useState(false);

  const activeAgents = agents.filter(agent => 
    agent.status === 'active' || agent.status === 'collaborating'
  );

  return (
    <div className="space-y-4">
      {/* Video Call Status */}
      <div className="text-center">
        <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
          isVideoCallActive 
            ? 'bg-green-100 dark:bg-green-900/30' 
            : 'bg-gray-100 dark:bg-gray-700'
        }`}>
          {isVideoCallActive ? (
            <Video className="w-8 h-8 text-green-600" />
          ) : (
            <VideoOff className="w-8 h-8 text-gray-400" />
          )}
        </div>
        
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {isVideoCallActive ? 'Live Session Active' : 'Start Video Session'}
        </h3>
        
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isVideoCallActive 
            ? `Connected with ${activeAgents.length} co-founder${activeAgents.length !== 1 ? 's' : ''}`
            : 'Connect with your AI co-founders via video'
          }
        </p>
      </div>

      {/* Active Participants */}
      {isVideoCallActive && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="space-y-3"
        >
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <Users size={16} />
            Active Participants
          </div>
          
          <div className="space-y-2">
            {activeAgents.map((agent) => (
              <div
                key={agent.id}
                className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-sm">
                  {agent.avatar}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {agent.name}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                    {agent.role}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-green-600">Live</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Video Preview */}
      <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
        <div className="absolute inset-0 flex items-center justify-center">
          {isCameraOn ? (
            <div className="text-center text-white">
              <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm opacity-75">Your camera feed will appear here</p>
            </div>
          ) : (
            <div className="text-center text-gray-400">
              <VideoOff className="w-12 h-12 mx-auto mb-2" />
              <p className="text-sm">Camera is off</p>
            </div>
          )}
        </div>

        {/* Video Controls Overlay */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
          <button
            onClick={() => setIsCameraOn(!isCameraOn)}
            className={`p-2 rounded-full transition-colors ${
              isCameraOn 
                ? 'bg-gray-800/80 text-white hover:bg-gray-700/80' 
                : 'bg-red-500 text-white hover:bg-red-600'
            }`}
          >
            {isCameraOn ? <Video size={16} /> : <VideoOff size={16} />}
          </button>
          
          <button
            onClick={() => setIsMicOn(!isMicOn)}
            className={`p-2 rounded-full transition-colors ${
              isMicOn 
                ? 'bg-gray-800/80 text-white hover:bg-gray-700/80' 
                : 'bg-red-500 text-white hover:bg-red-600'
            }`}
          >
            {isMicOn ? <Mic size={16} /> : <MicOff size={16} />}
          </button>
          
          <button
            onClick={() => setIsScreenSharing(!isScreenSharing)}
            className={`p-2 rounded-full transition-colors ${
              isScreenSharing 
                ? 'bg-blue-500 text-white hover:bg-blue-600' 
                : 'bg-gray-800/80 text-white hover:bg-gray-700/80'
            }`}
          >
            <Monitor size={16} />
          </button>
        </div>
      </div>

      {/* Call Controls */}
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={toggleVideoCall}
          className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            isVideoCallActive
              ? 'bg-red-500 hover:bg-red-600 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
        >
          {isVideoCallActive ? (
            <>
              <PhoneOff size={16} />
              End Session
            </>
          ) : (
            <>
              <Phone size={16} />
              Start Session
            </>
          )}
        </button>
        
        <button className="p-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
          <Settings size={16} />
        </button>
      </div>

      {/* Quick Actions */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button className="p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
            <p className="font-medium text-gray-900 dark:text-white text-sm">Schedule Meeting</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Plan ahead</p>
          </button>
          <button className="p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors">
            <p className="font-medium text-gray-900 dark:text-white text-sm">Record Session</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Save insights</p>
          </button>
        </div>
      </div>
    </div>
  );
};