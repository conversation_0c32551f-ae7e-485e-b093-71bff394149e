import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Video, 
  VideoOff, 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Monitor,
  MonitorOff,
  Users,
  MessageSquare
} from 'lucide-react';
import { liveKit, LiveKitSession, LiveKitMessage } from '../services/livekit';
import toast from 'react-hot-toast';

interface LiveKitRoomProps {
  session: LiveKitSession;
  onEnd: () => void;
}

export const LiveKitRoom: React.FC<LiveKitRoomProps> = ({ session, onEnd }) => {
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [messages, setMessages] = useState<LiveKitMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');

  useEffect(() => {
    const handleMessage = (message: LiveKitMessage) => {
      setMessages(prev => [...prev, message]);
    };

    liveKit.onMessage(handleMessage);

    return () => {
      liveKit.offMessage(handleMessage);
    };
  }, []);

  const toggleVideo = async () => {
    try {
      if (isVideoEnabled) {
        await liveKit.disableCamera();
      } else {
        await liveKit.enableCamera();
      }
      setIsVideoEnabled(!isVideoEnabled);
    } catch (error) {
      console.error('Error toggling video:', error);
      toast.error('Failed to toggle video');
    }
  };

  const toggleAudio = async () => {
    try {
      if (isAudioEnabled) {
        await liveKit.disableMicrophone();
      } else {
        await liveKit.enableMicrophone();
      }
      setIsAudioEnabled(!isAudioEnabled);
    } catch (error) {
      console.error('Error toggling audio:', error);
      toast.error('Failed to toggle audio');
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        await liveKit.stopScreenShare();
      } else {
        await liveKit.startScreenShare();
      }
      setIsScreenSharing(!isScreenSharing);
    } catch (error) {
      console.error('Error toggling screen share:', error);
      toast.error('Failed to toggle screen share');
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      await liveKit.sendMessage(newMessage);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleEndSession = async () => {
    try {
      await liveKit.endSession(session.id);
      onEnd();
      toast.success('Session ended successfully');
    } catch (error) {
      console.error('Error ending session:', error);
      toast.error('Failed to end session');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-gray-900 z-50 flex flex-col"
    >
      {/* Header */}
      <div className="bg-gray-800 p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-white text-lg font-semibold">LiveKit Session</h2>
          <div className="flex items-center gap-2 text-green-400">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm">Live</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2 text-white">
          <Users size={16} />
          <span className="text-sm">{session.participants.length} participants</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Video Grid */}
        <div className="flex-1 grid grid-cols-2 md:grid-cols-3 gap-4 p-4">
          {session.participants.map((participant) => (
            <motion.div
              key={participant.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gray-800 rounded-lg overflow-hidden relative aspect-video"
            >
              {/* Video Placeholder */}
              <div className="w-full h-full bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
                {participant.videoEnabled ? (
                  <div className="text-center text-white">
                    <Video className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm opacity-75">Video feed</p>
                  </div>
                ) : (
                  <div className="text-center text-white">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <span className="text-2xl font-bold">
                        {participant.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm">{participant.name}</p>
                  </div>
                )}
              </div>

              {/* Participant Info */}
              <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
                <span className="text-white text-sm font-medium bg-black/50 px-2 py-1 rounded">
                  {participant.name}
                </span>
                <div className="flex items-center gap-1">
                  {!participant.audioEnabled && (
                    <MicOff className="w-4 h-4 text-red-400" />
                  )}
                  {!participant.videoEnabled && (
                    <VideoOff className="w-4 h-4 text-red-400" />
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Chat Sidebar */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-white font-medium flex items-center gap-2">
              <MessageSquare size={16} />
              Chat
            </h3>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {messages.map((message) => (
              <div key={message.id} className="text-sm">
                <div className="text-gray-400 text-xs mb-1">
                  {message.participantId} • {message.timestamp.toLocaleTimeString()}
                </div>
                <div className="text-white">{message.content}</div>
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Type a message..."
                className="flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button
                onClick={sendMessage}
                className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Send
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-gray-800 p-4 flex items-center justify-center gap-4">
        <button
          onClick={toggleAudio}
          className={`p-3 rounded-full transition-colors ${
            isAudioEnabled 
              ? 'bg-gray-700 hover:bg-gray-600 text-white' 
              : 'bg-red-500 hover:bg-red-600 text-white'
          }`}
        >
          {isAudioEnabled ? <Mic size={20} /> : <MicOff size={20} />}
        </button>

        <button
          onClick={toggleVideo}
          className={`p-3 rounded-full transition-colors ${
            isVideoEnabled 
              ? 'bg-gray-700 hover:bg-gray-600 text-white' 
              : 'bg-red-500 hover:bg-red-600 text-white'
          }`}
        >
          {isVideoEnabled ? <Video size={20} /> : <VideoOff size={20} />}
        </button>

        <button
          onClick={toggleScreenShare}
          className={`p-3 rounded-full transition-colors ${
            isScreenSharing 
              ? 'bg-blue-500 hover:bg-blue-600 text-white' 
              : 'bg-gray-700 hover:bg-gray-600 text-white'
          }`}
        >
          {isScreenSharing ? <MonitorOff size={20} /> : <Monitor size={20} />}
        </button>

        <button
          onClick={handleEndSession}
          className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <PhoneOff size={16} />
          End Session
        </button>
      </div>
    </motion.div>
  );
};