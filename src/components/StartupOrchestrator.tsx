import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Users, 
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Download,
  FileText,
  CheckCircle,
  Clock,
  ArrowRight,
  TrendingUp,
  Rocket,
  Wrench,
  Briefcase,
  Megaphone,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

interface AgentOutput {
  agentName: string;
  agentType: 'strategic' | 'product' | 'technical' | 'operations' | 'marketing';
  status: 'pending' | 'running' | 'completed';
  duration: number;
  liveKitUrl?: string;
  additionalUrls?: string[];
  markdownReport: string;
  keyInsights: string[];
}

interface OrchestratorSession {
  url: string;
  isActive: boolean;
  transcript: string[];
  duration: number;
}

export const StartupOrchestrator: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'intro' | 'welcome' | 'orchestrating' | 'complete'>('intro');
  const [userIdea, setUserIdea] = useState('');
  const [orchestratorSession, setOrchestratorSession] = useState<OrchestratorSession>({
    url: '',
    isActive: false,
    transcript: [],
    duration: 0
  });
  const [agentOutputs, setAgentOutputs] = useState<AgentOutput[]>([
    {
      agentName: 'Strategic Co-founder',
      agentType: 'strategic',
      status: 'pending',
      duration: 0,
      markdownReport: '',
      keyInsights: []
    },
    {
      agentName: 'Product Co-founder',
      agentType: 'product',
      status: 'pending',
      duration: 0,
      markdownReport: '',
      keyInsights: []
    },
    {
      agentName: 'Technical Co-founder',
      agentType: 'technical',
      status: 'pending',
      duration: 0,
      markdownReport: '',
      keyInsights: []
    },
    {
      agentName: 'Operations Co-founder',
      agentType: 'operations',
      status: 'pending',
      duration: 0,
      markdownReport: '',
      keyInsights: []
    },
    {
      agentName: 'Marketing Co-founder',
      agentType: 'marketing',
      status: 'pending',
      duration: 0,
      markdownReport: '',
      keyInsights: []
    }
  ]);
  const [currentAgentIndex, setCurrentAgentIndex] = useState(-1);
  const [isProcessing, setIsProcessing] = useState(false);

  const startOrchestratorSession = () => {
    setOrchestratorSession({
      url: 'wss://orchestrator.livekit.cloud',
      isActive: true,
      transcript: [
        "Orchestrator: Welcome! I'm your Startup Co-founder Orchestrator. I'll coordinate all five AI co-founders to analyze your business idea comprehensively.",
        "Orchestrator: I'll guide you through strategic analysis, product validation, technical architecture, operational setup, and marketing strategy.",
        "Orchestrator: Each agent will provide specialized insights and we'll compile everything into a master startup report."
      ],
      duration: 0
    });
    setCurrentStep('welcome');

    // Simulate welcome session timer
    const timer = setInterval(() => {
      setOrchestratorSession(prev => ({
        ...prev,
        duration: prev.duration + 1
      }));
    }, 1000);

    // Auto-end welcome after 1 minute
    setTimeout(() => {
      clearInterval(timer);
      endWelcomeSession();
    }, 60000);
  };

  const endWelcomeSession = () => {
    setOrchestratorSession(prev => ({ ...prev, isActive: false }));
    setCurrentStep('orchestrating');
    startAgentOrchestration();
  };

  const startAgentOrchestration = async () => {
    setIsProcessing(true);
    setCurrentAgentIndex(0);

    // Simulate each agent running sequentially
    for (let i = 0; i < agentOutputs.length; i++) {
      setCurrentAgentIndex(i);
      
      // Update agent status to running
      setAgentOutputs(prev => prev.map((agent, index) => 
        index === i ? { ...agent, status: 'running' } : agent
      ));

      // Simulate agent processing time (2-5 minutes each)
      const processingTime = 2000 + (i * 500); // Staggered timing
      await new Promise(resolve => setTimeout(resolve, processingTime));

      // Generate mock output for each agent
      const mockOutput = generateMockAgentOutput(agentOutputs[i].agentType);
      
      setAgentOutputs(prev => prev.map((agent, index) => 
        index === i ? { 
          ...agent, 
          status: 'completed',
          duration: Math.floor(processingTime / 1000),
          ...mockOutput
        } : agent
      ));
    }

    setCurrentAgentIndex(-1);
    setIsProcessing(false);
    setCurrentStep('complete');
  };

  const generateMockAgentOutput = (agentType: string) => {
    const baseUrl = 'https://livekit-sessions.s3.amazonaws.com';
    
    switch (agentType) {
      case 'strategic':
        return {
          liveKitUrl: `${baseUrl}/strategic-briefing-${Date.now()}.mp4`,
          markdownReport: `# Strategic Market Analysis

## Market Sizing (TAM/SAM/SOM)
- **TAM:** $2.3T - Global Software Market
- **SAM:** $180B - AI-Powered Business Tools  
- **SOM:** $2.1B - AI Co-founder & Business Intelligence Tools

## Competitive Landscape
### Direct Competitors
- **Harvey AI** (3%): AI legal co-pilot for professional services
- **Anthropic Claude** (5%): General-purpose AI assistant for business

### Indirect Competitors  
- **Jasper AI** (15%): AI content creation and marketing assistant
- **Copy.ai** (12%): AI writing and content generation platform
- **Notion AI** (8%): Integrated AI workspace and productivity tools

## White-Space Opportunities
1. Multi-agent collaboration systems with specialized business roles
2. Real-time video/voice AI co-founder interactions
3. Integrated code generation with business strategy alignment

## 6-Month Go-to-Market Roadmap
### Month 1-2: Foundation & Beta
- MVP development with core 5-agent system
- Beta testing with 50 early-stage startups
- LiveKit integration for seamless video/voice
- Initial Composio integrations (Slack, GitHub)

### Month 3-4: Launch & Growth
- Public launch with freemium model
- Content marketing and thought leadership
- Partnership with accelerators and VCs
- Advanced analytics and reporting features

### Month 5-6: Scale & Expansion
- Enterprise tier with custom agent training
- API marketplace for third-party integrations
- International expansion (EU, APAC)
- Series A fundraising preparation`,
          keyInsights: [
            'Large addressable market with $2.1B SOM opportunity',
            'Limited direct competition in multi-agent AI co-founder space',
            'Strong white-space in integrated business intelligence tools',
            'Clear 6-month roadmap to market leadership'
          ]
        };

      case 'product':
        return {
          liveKitUrl: `${baseUrl}/product-interviews-${Date.now()}.mp4`,
          additionalUrls: [
            `${baseUrl}/interview-restaurant-owner.mp4`,
            `${baseUrl}/interview-manager.mp4`,
            `${baseUrl}/interview-chef.mp4`
          ],
          markdownReport: `# Product Strategy Report

## Interview Summary
Conducted 3 LiveKit video interviews (5 minutes each) with target users.

## User Personas
### Alice Chen (Restaurant Owner)
**Demographics:** 35-45, 10+ years in hospitality, Moderate tech-savvy

**Pain Points:**
- Manual reservation system leads to double bookings
- Difficulty tracking table turnover rates
- No integrated payment processing
- Staff scheduling conflicts

**Goals:**
- Increase table utilization by 25%
- Reduce no-shows through automated reminders
- Streamline payment processing
- Improve staff coordination

## Feature Prioritization (RICE)
| Feature | Reach | Impact | Confidence | Effort | Score |
|---------|-------|--------|------------|--------|-------|
| Smart Reservation System | 85 | 9 | 8 | 5 | 1224 |
| Digital Menu Management | 70 | 8 | 9 | 4 | 1260 |
| Analytics Dashboard | 60 | 7 | 8 | 6 | 560 |

## 5-Step User Onboarding Flow
1. **Welcome & Video Introduction**
   Personalized welcome email with 2-minute product demo video

2. **Restaurant Profile Setup**
   Basic information: name, cuisine type, seating capacity, hours

3. **Interactive Feature Tour**
   Guided walkthrough of reservation system, menu management, and analytics

4. **Create First Menu Item**
   Add signature dish with pricing, ingredients, and dietary information

5. **Go Live & Share**
   Activate reservation system and generate shareable booking link`,
          keyInsights: [
            'Strong demand for integrated restaurant management solutions',
            'Digital menu management scores highest in RICE prioritization',
            'Users prefer guided onboarding with immediate value demonstration',
            'Video interviews revealed critical pain points in manual processes'
          ]
        };

      case 'technical':
        return {
          liveKitUrl: `${baseUrl}/technical-demo-${Date.now()}.mp4`,
          markdownReport: `# Technical Architecture Report

## Tech Stack Recommendation
**Selected: Modern Full-Stack (React + Node.js + PostgreSQL)**

### Pros:
- Rapid development with familiar technologies
- Strong ecosystem and community support
- Excellent scalability with proper architecture
- Cost-effective for MVP and growth phases

### Cons:
- Requires careful architecture for enterprise scale
- Need strong DevOps practices for production

## System Architecture
\`\`\`
Client (React) → API Gateway → Microservices → Database (PostgreSQL)
                    ↓
            LiveKit (Video/Voice) → AI Agents (CrewAI)
                    ↓
            Composio (Integrations) → External APIs
\`\`\`

## MVP Scaffold Structure
\`\`\`
/ultimate-cofounder
├── /frontend (React + TypeScript)
├── /backend (Node.js + Express)
├── /agents (CrewAI + LangChain)
├── /integrations (Composio)
├── /database (PostgreSQL + Prisma)
└── /infrastructure (Docker + AWS)
\`\`\`

## Automated Test Cases
1. **Agent Communication Test**: Verify multi-agent coordination
2. **LiveKit Integration Test**: Test video/voice session creation
3. **API Performance Test**: Load testing for concurrent users
4. **Data Security Test**: Validate encryption and access controls
5. **Integration Test**: Verify Composio third-party connections`,
          keyInsights: [
            'Modern full-stack approach provides optimal balance of speed and scalability',
            'Microservices architecture enables independent agent scaling',
            'LiveKit integration requires careful session management',
            'Comprehensive testing strategy essential for multi-agent reliability'
          ]
        };

      case 'operations':
        return {
          liveKitUrl: `${baseUrl}/operations-briefing-${Date.now()}.mp4`,
          markdownReport: `# Operations Strategy Report

## Legal & Compliance Checklist
1. **Business Incorporation** (HIGH priority)
   - Timeline: Week 1-2
   - Register as Delaware C-Corp for investor readiness

2. **Privacy Policy & GDPR Compliance** (HIGH priority)
   - Timeline: Week 2-3
   - Comprehensive privacy policy covering data collection and processing

3. **Terms of Service** (HIGH priority)
   - Timeline: Week 2-3
   - User agreement covering service usage and liability

4. **Data Security Audit** (MEDIUM priority)
   - Timeline: Month 2-3
   - SOC 2 Type II compliance and security framework

5. **IP Assignment Agreements** (HIGH priority)
   - Timeline: Week 1
   - Intellectual property assignment for all team members

## 3-Year Financial Model
| Year | Revenue | COGS | OpEx | Gross Profit | Net Profit |
|------|---------|------|------|--------------|------------|
| 1 | $250,000 | $75,000 | $180,000 | $175,000 | -$5,000 |
| 2 | $850,000 | $255,000 | $420,000 | $595,000 | $175,000 |
| 3 | $2,100,000 | $630,000 | $890,000 | $1,470,000 | $580,000 |

## 6-Month Hiring Plan
**Month 1: Senior Backend Engineer** (Engineering) - $140,000
**Month 2: UI/UX Designer** (Product) - $95,000
**Month 3: Frontend Engineer** (Engineering) - $120,000
**Month 4: DevOps Engineer** (Engineering) - $130,000
**Month 5: Sales Development Rep** (Sales) - $65,000
**Month 6: Customer Success Manager** (Customer Success) - $85,000

### Total 6-Month Hiring Budget: $635,000

## Process Optimizations
1. **CI/CD Pipeline Automation** (DEVELOPMENT)
   - Impact: HIGH | Effort: MEDIUM | Timeline: Week 2-3

2. **Weekly Sprint Reviews** (BUSINESS)
   - Impact: MEDIUM | Effort: LOW | Timeline: Week 1

3. **Automated Monitoring & Alerts** (OPERATIONS)
   - Impact: HIGH | Effort: MEDIUM | Timeline: Week 3-4`,
          keyInsights: [
            'Delaware C-Corp incorporation critical for investor readiness',
            'Path to profitability by Year 2 with strong growth trajectory',
            'Strategic hiring plan focuses on core technical capabilities first',
            'Process automation provides high-impact operational efficiency'
          ]
        };

      case 'marketing':
        return {
          liveKitUrl: `${baseUrl}/marketing-pitch-${Date.now()}.mp4`,
          markdownReport: `# Marketing Strategy Report

## Brand Positioning
**Tagline:** "Your AI Co-founder Team, Ready in Minutes"

**Value Proposition:** The first comprehensive AI co-founder platform that provides strategic, product, technical, operations, and marketing expertise through specialized AI agents with real-time video/voice collaboration.

## 3-Month Content Calendar
| Week | Topic | Channel |
|------|-------|---------|
| 1 | Launch announcement | Twitter, LinkedIn |
| 2 | AI Co-founder Demo Video | YouTube, Blog |
| 3 | Startup Success Stories | Newsletter, LinkedIn |
| 4 | Technical Deep Dive | Blog, Twitter |
| 5 | Product Hunt Launch | All Channels |
| 6 | Founder Interview Series | YouTube, Podcast |
| 7 | Integration Showcase | Blog, LinkedIn |
| 8 | Customer Case Studies | All Channels |
| 9 | AI Trends Analysis | Blog, Newsletter |
| 10 | Community Building | Discord, Twitter |
| 11 | Partnership Announcements | LinkedIn, PR |
| 12 | Year-End Retrospective | All Channels |

## Growth Hacks
1. **AI Startup Accelerator Partnerships** ($2,500)
   - Partner with Y Combinator, Techstars for exclusive access
   - Expected: 500 qualified leads, 50 conversions

2. **Viral LinkedIn Challenge** ($500)
   - #AICofounderChallenge with startup pitch videos
   - Expected: 10K impressions, 200 signups

3. **Product Hunt Launch Strategy** ($1,500)
   - Coordinated launch with influencer network
   - Expected: Top 5 daily ranking, 1K signups

4. **Startup Podcast Tour** ($800)
   - 20 podcast appearances over 3 months
   - Expected: 5K qualified leads, 100 conversions

### Total Investment: $5,300 | Expected ROI: 300%

## Press Release
**Headline:** "Revolutionary AI Platform Launches 5-Agent Co-founder System for Startups"

**Opening:** Today marks the launch of Ultimate Startup Co-founder, the first comprehensive AI platform that provides entrepreneurs with a complete team of specialized AI co-founders. The platform features five distinct AI agents covering strategic planning, product development, technical architecture, operations management, and marketing strategy, all accessible through real-time video and voice interactions powered by LiveKit technology.`,
          keyInsights: [
            'Strong brand positioning as first comprehensive AI co-founder platform',
            'Multi-channel content strategy drives consistent engagement',
            'Growth hacks focus on startup ecosystem partnerships for qualified leads',
            'Press release emphasizes first-mover advantage in AI co-founder space'
          ]
        };

      default:
        return {
          liveKitUrl: `${baseUrl}/default-session-${Date.now()}.mp4`,
          markdownReport: '# Agent Report\n\nReport content here...',
          keyInsights: ['Key insight 1', 'Key insight 2']
        };
    }
  };

  const generateMasterReport = () => {
    const totalDuration = agentOutputs.reduce((total, agent) => total + agent.duration, 0);
    const totalSessions = agentOutputs.length + 1; // +1 for orchestrator session
    
    return `# Ultimate Startup Report
*Generated by AI Co-founder Orchestrator*

## Executive Summary
This comprehensive startup analysis was conducted by our 5-agent AI co-founder system, featuring specialized agents for strategic planning, product development, technical architecture, operations management, and marketing strategy. Each agent provided deep domain expertise through LiveKit video/voice sessions and detailed analysis.

**Total Analysis Time:** ${Math.floor(totalDuration / 60)}:${(totalDuration % 60).toString().padStart(2, '0')}
**LiveKit Sessions:** ${totalSessions}
**Business Idea:** ${userIdea || 'AI-powered startup co-founder platform'}

---

## 1. Strategic Analysis
${agentOutputs[0].markdownReport}

**LiveKit Session:** [Strategic Briefing](${agentOutputs[0].liveKitUrl})

**Key Strategic Insights:**
${agentOutputs[0].keyInsights.map(insight => `- ${insight}`).join('\n')}

---

## 2. Product Strategy
${agentOutputs[1].markdownReport}

**LiveKit Sessions:** 
- [Product Strategy Session](${agentOutputs[1].liveKitUrl})
${agentOutputs[1].additionalUrls ? agentOutputs[1].additionalUrls.map((url, i) => `- [User Interview ${i + 1}](${url})`).join('\n') : ''}

**Key Product Insights:**
${agentOutputs[1].keyInsights.map(insight => `- ${insight}`).join('\n')}

---

## 3. Technical Architecture
${agentOutputs[2].markdownReport}

**LiveKit Session:** [Technical Demo](${agentOutputs[2].liveKitUrl})

**Key Technical Insights:**
${agentOutputs[2].keyInsights.map(insight => `- ${insight}`).join('\n')}

---

## 4. Operations Foundation
${agentOutputs[3].markdownReport}

**LiveKit Session:** [Operations Briefing](${agentOutputs[3].liveKitUrl})

**Key Operations Insights:**
${agentOutputs[3].keyInsights.map(insight => `- ${insight}`).join('\n')}

---

## 5. Marketing Strategy
${agentOutputs[4].markdownReport}

**LiveKit Session:** [Marketing Pitch Rehearsal](${agentOutputs[4].liveKitUrl})

**Key Marketing Insights:**
${agentOutputs[4].keyInsights.map(insight => `- ${insight}`).join('\n')}

---

## Master Recommendations

### Immediate Actions (Week 1-2)
1. **Legal Foundation:** Incorporate as Delaware C-Corp and establish IP assignments
2. **Technical Setup:** Begin MVP development with recommended tech stack
3. **Team Building:** Hire Senior Backend Engineer and UI/UX Designer
4. **Market Validation:** Launch beta program with 50 early-stage startups

### Short-term Goals (Month 1-3)
1. **Product Development:** Complete core 5-agent system with LiveKit integration
2. **Legal Compliance:** Finalize privacy policy, terms of service, and security audit
3. **Go-to-Market:** Execute content marketing strategy and partnership development
4. **Funding Preparation:** Prepare pitch deck and financial projections for seed round

### Long-term Vision (Month 4-6)
1. **Market Launch:** Public launch with freemium model and enterprise tier
2. **Scale Operations:** Complete hiring plan and process optimizations
3. **International Expansion:** Enter EU and APAC markets
4. **Series A Preparation:** Prepare for Series A fundraising with proven traction

---

## Success Metrics & KPIs

### Financial Targets
- **Year 1:** $250K revenue, break-even by Q4
- **Year 2:** $850K revenue, 20% net margin
- **Year 3:** $2.1M revenue, 28% net margin

### Product Metrics
- **Beta Phase:** 50 active users, 80% retention
- **Launch Phase:** 1,000 signups, 15% conversion to paid
- **Growth Phase:** 10,000 users, $100 average revenue per user

### Market Position
- **6 Months:** Top 3 AI co-founder platform
- **12 Months:** Market leader in multi-agent business intelligence
- **18 Months:** International presence in 3 markets

---

*This report represents the collective intelligence of our AI co-founder team. Each section includes detailed analysis, actionable recommendations, and supporting LiveKit session recordings for comprehensive startup guidance.*

**Orchestrator Session:** [Welcome & Coordination](${orchestratorSession.url})
**Report Generated:** ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`;
  };

  const downloadMasterReport = () => {
    const report = generateMasterReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ultimate-startup-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'strategic': return TrendingUp;
      case 'product': return Rocket;
      case 'technical': return Wrench;
      case 'operations': return Briefcase;
      case 'marketing': return Megaphone;
      default: return Users;
    }
  };

  const getAgentColor = (agentType: string) => {
    switch (agentType) {
      case 'strategic': return 'from-blue-500 to-purple-600';
      case 'product': return 'from-purple-500 to-pink-600';
      case 'technical': return 'from-green-500 to-blue-600';
      case 'operations': return 'from-green-500 to-blue-600';
      case 'marketing': return 'from-pink-500 to-red-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Zap className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Startup Co-founder Orchestrator
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Comprehensive 5-agent startup analysis with LiveKit coordination
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        {[
          { id: 'intro', label: 'Introduction', icon: Users },
          { id: 'welcome', label: 'Welcome Session', icon: Video },
          { id: 'orchestrating', label: 'Agent Coordination', icon: Zap },
          { id: 'complete', label: 'Master Report', icon: FileText }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              currentStep === step.id 
                ? 'bg-purple-600 text-white' 
                : index < ['intro', 'welcome', 'orchestrating', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={16} />
            </div>
            <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              {step.label}
            </span>
            {index < 3 && (
              <div className={`w-8 h-0.5 mx-4 ${
                index < ['intro', 'welcome', 'orchestrating', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Complete Startup Analysis with AI Co-founder Team
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll coordinate all five specialized AI co-founders to provide comprehensive startup analysis. Each agent will conduct LiveKit sessions and generate detailed reports, which I'll compile into a master startup strategy document.
            </p>
            
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Your Business Idea
                </label>
                <textarea
                  value={userIdea}
                  onChange={(e) => setUserIdea(e.target.value)}
                  placeholder="Describe your startup idea in detail. This will be shared with all AI co-founders for comprehensive analysis..."
                  className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  rows={4}
                />
              </div>
            </div>

            {/* Agent Preview */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
              {agentOutputs.map((agent, index) => {
                const IconComponent = getAgentIcon(agent.agentType);
                return (
                  <div key={index} className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className={`w-12 h-12 bg-gradient-to-br ${getAgentColor(agent.agentType)} rounded-full flex items-center justify-center mx-auto mb-2`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                      {agent.agentName}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                      {agent.agentType} analysis
                    </p>
                  </div>
                );
              })}
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={startOrchestratorSession}
              disabled={!userIdea.trim()}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-semibold text-lg transition-all disabled:cursor-not-allowed"
            >
              <Video size={20} />
              Start LiveKit Orchestration Session
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'welcome' && (
          <motion.div
            key="welcome"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Video className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Live Orchestrator Session Active
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(orchestratorSession.duration / 60)}:{(orchestratorSession.duration % 60).toString().padStart(2, '0')} / 1:00
              </p>
            </div>

            {/* Video Interface */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6 aspect-video relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Video className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Orchestrator Welcome Session</p>
                  <p className="text-sm opacity-75">Coordinating AI co-founder team</p>
                </div>
              </div>
              
              {/* Video Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Mic size={16} />
                </button>
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Video size={16} />
                </button>
                <button className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600">
                  <PhoneOff size={16} />
                </button>
              </div>
            </div>

            {/* Live Transcript */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 max-h-40 overflow-y-auto">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Live Transcript</h3>
              <div className="space-y-2">
                {orchestratorSession.transcript.map((message, index) => (
                  <p key={index} className="text-sm text-gray-600 dark:text-gray-400">
                    {message}
                  </p>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={endWelcomeSession}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
              >
                <ArrowRight size={16} />
                Begin Agent Coordination
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <Pause size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'orchestrating' && (
          <motion.div
            key="orchestrating"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                AI Co-founder Coordination in Progress
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Each agent is conducting specialized analysis and generating comprehensive reports
              </p>
            </div>

            {/* Agent Progress */}
            <div className="space-y-4">
              {agentOutputs.map((agent, index) => {
                const IconComponent = getAgentIcon(agent.agentType);
                const isActive = currentAgentIndex === index;
                const isCompleted = agent.status === 'completed';
                const isPending = agent.status === 'pending';
                
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border-2 ${
                      isActive ? 'border-purple-500' : 
                      isCompleted ? 'border-green-500' : 
                      'border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          isCompleted ? 'bg-green-500' :
                          isActive ? 'bg-purple-500 animate-pulse' :
                          'bg-gray-300 dark:bg-gray-600'
                        }`}>
                          {isCompleted ? (
                            <CheckCircle className="w-6 h-6 text-white" />
                          ) : isActive ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <IconComponent className="w-6 h-6 text-white" />
                          )}
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                            {agent.agentName}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                            {agent.agentType} analysis
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        {isCompleted && (
                          <div className="text-right">
                            <p className="text-sm font-medium text-green-600">Completed</p>
                            <p className="text-xs text-gray-500">
                              {Math.floor(agent.duration / 60)}:{(agent.duration % 60).toString().padStart(2, '0')}
                            </p>
                          </div>
                        )}
                        {isActive && (
                          <div className="text-right">
                            <p className="text-sm font-medium text-purple-600">Running...</p>
                            <p className="text-xs text-gray-500">Analyzing</p>
                          </div>
                        )}
                        {isPending && (
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-500">Pending</p>
                            <p className="text-xs text-gray-400">Waiting</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {isCompleted && agent.keyInsights.length > 0 && (
                      <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <h4 className="font-medium text-green-900 dark:text-green-200 mb-2">Key Insights</h4>
                        <ul className="space-y-1">
                          {agent.keyInsights.slice(0, 2).map((insight, i) => (
                            <li key={i} className="text-sm text-green-700 dark:text-green-300 flex items-start gap-2">
                              <div className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                              {insight}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>

            {/* Overall Progress */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">Overall Progress</h3>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {agentOutputs.filter(a => a.status === 'completed').length} / {agentOutputs.length} Complete
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-600 h-3 rounded-full transition-all duration-1000"
                  style={{ 
                    width: `${(agentOutputs.filter(a => a.status === 'completed').length / agentOutputs.length) * 100}%` 
                  }}
                />
              </div>
            </div>
          </motion.div>
        )}

        {currentStep === 'complete' && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Ultimate Startup Report Complete
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Comprehensive analysis from all 5 AI co-founders with LiveKit sessions
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {agentOutputs.map((agent, index) => {
                const IconComponent = getAgentIcon(agent.agentType);
                return (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                    <div className={`w-10 h-10 bg-gradient-to-br ${getAgentColor(agent.agentType)} rounded-full flex items-center justify-center mx-auto mb-2`}>
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {Math.floor(agent.duration / 60)}:{(agent.duration % 60).toString().padStart(2, '0')}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                      {agent.agentType}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* LiveKit Sessions Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">LiveKit Sessions</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Video className="w-5 h-5 text-purple-600" />
                    <span className="font-medium text-gray-900 dark:text-white">Orchestrator Welcome</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {Math.floor(orchestratorSession.duration / 60)}:{(orchestratorSession.duration % 60).toString().padStart(2, '0')}
                  </span>
                </div>
                {agentOutputs.map((agent, index) => {
                  const IconComponent = getAgentIcon(agent.agentType);
                  return (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <IconComponent className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <span className="font-medium text-gray-900 dark:text-white">{agent.agentName}</span>
                        {agent.additionalUrls && (
                          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full">
                            +{agent.additionalUrls.length} clips
                          </span>
                        )}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {Math.floor(agent.duration / 60)}:{(agent.duration % 60).toString().padStart(2, '0')}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadMasterReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Master Report
              </motion.button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setUserIdea('');
                  setAgentOutputs(prev => prev.map(agent => ({ 
                    ...agent, 
                    status: 'pending', 
                    duration: 0, 
                    markdownReport: '', 
                    keyInsights: [] 
                  })));
                  setOrchestratorSession(prev => ({ ...prev, transcript: [], duration: 0 }));
                  setCurrentAgentIndex(-1);
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                <RotateCcw size={16} />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};