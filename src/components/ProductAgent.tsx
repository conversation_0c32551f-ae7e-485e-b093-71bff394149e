import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, 
  Users, 
  Star, 
  Target, 
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Download,
  FileText,
  BarChart3,
  Lightbulb,
  Monitor,
  Share2,
  Play,
  Pause,
  SkipForward
} from 'lucide-react';

interface Persona {
  name: string;
  role: string;
  painPoints: string[];
  goals: string[];
  demographics: {
    age: string;
    experience: string;
    techSavvy: string;
  };
}

interface Feature {
  name: string;
  description: string;
  reach: number;
  impact: number;
  confidence: number;
  effort: number;
  score: number;
}

interface OnboardingStep {
  step: number;
  title: string;
  description: string;
  wireframe?: string;
}

interface Interview {
  id: number;
  persona: string;
  duration: number;
  status: 'pending' | 'active' | 'completed';
  clipUrl?: string;
  keyInsights: string[];
}

interface WireframeScreen {
  id: string;
  name: string;
  description: string;
  elements: string[];
}

export const ProductAgent: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'intro' | 'interviews' | 'personas' | 'features' | 'onboarding' | 'wireframes' | 'complete'>('intro');
  const [interviews, setInterviews] = useState<Interview[]>([
    { id: 1, persona: 'Restaurant Owner', duration: 0, status: 'pending', keyInsights: [] },
    { id: 2, persona: 'Manager', duration: 0, status: 'pending', keyInsights: [] },
    { id: 3, persona: 'Chef', duration: 0, status: 'pending', keyInsights: [] }
  ]);
  const [currentInterview, setCurrentInterview] = useState<Interview | null>(null);
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [onboardingFlow, setOnboardingFlow] = useState<OnboardingStep[]>([]);
  const [wireframes, setWireframes] = useState<WireframeScreen[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);

  const startInterview = (interview: Interview) => {
    setCurrentInterview(interview);
    setInterviews(prev => prev.map(i => 
      i.id === interview.id ? { ...i, status: 'active' } : i
    ));
    setCurrentStep('interviews');

    // Simulate 5-minute interview timer
    const timer = setInterval(() => {
      setCurrentInterview(prev => {
        if (!prev) return null;
        const newDuration = prev.duration + 1;
        
        // Auto-end after 5 minutes (300 seconds)
        if (newDuration >= 300) {
          clearInterval(timer);
          completeInterview(prev);
          return { ...prev, duration: 300, status: 'completed' };
        }
        
        return { ...prev, duration: newDuration };
      });
    }, 1000);
  };

  const completeInterview = (interview: Interview) => {
    const mockInsights = {
      'Restaurant Owner': [
        'Struggles with manual reservation management',
        'Needs real-time table availability updates',
        'Wants integrated payment processing',
        'Requires staff scheduling coordination'
      ],
      'Manager': [
        'Needs comprehensive analytics dashboard',
        'Wants automated inventory tracking',
        'Requires customer feedback management',
        'Needs multi-location support'
      ],
      'Chef': [
        'Wants digital menu management',
        'Needs ingredient availability alerts',
        'Requires kitchen workflow optimization',
        'Wants recipe standardization tools'
      ]
    };

    setInterviews(prev => prev.map(i => 
      i.id === interview.id 
        ? { 
            ...i, 
            status: 'completed',
            clipUrl: `https://livekit-recordings.s3.amazonaws.com/interview-${i.id}.mp4`,
            keyInsights: mockInsights[interview.persona as keyof typeof mockInsights] || []
          }
        : i
    ));
    setCurrentInterview(null);

    // Check if all interviews are complete
    const allComplete = interviews.every(i => 
      i.id === interview.id || i.status === 'completed'
    );
    
    if (allComplete) {
      generatePersonas();
    }
  };

  const generatePersonas = async () => {
    setIsProcessing(true);
    setCurrentStep('personas');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const mockPersonas: Persona[] = [
      {
        name: 'Alice Chen',
        role: 'Restaurant Owner',
        painPoints: [
          'Manual reservation system leads to double bookings',
          'Difficulty tracking table turnover rates',
          'No integrated payment processing',
          'Staff scheduling conflicts'
        ],
        goals: [
          'Increase table utilization by 25%',
          'Reduce no-shows through automated reminders',
          'Streamline payment processing',
          'Improve staff coordination'
        ],
        demographics: {
          age: '35-45',
          experience: '10+ years in hospitality',
          techSavvy: 'Moderate'
        }
      },
      {
        name: 'Bob Martinez',
        role: 'Restaurant Manager',
        painPoints: [
          'Lack of real-time analytics',
          'Manual inventory management',
          'Inconsistent customer service',
          'Multi-location coordination challenges'
        ],
        goals: [
          'Optimize operational efficiency',
          'Improve customer satisfaction scores',
          'Reduce food waste by 20%',
          'Standardize processes across locations'
        ],
        demographics: {
          age: '28-38',
          experience: '5-8 years in management',
          techSavvy: 'High'
        }
      },
      {
        name: 'Charlie Thompson',
        role: 'Head Chef',
        painPoints: [
          'Paper-based recipe management',
          'Ingredient shortage surprises',
          'Kitchen workflow bottlenecks',
          'Inconsistent dish quality'
        ],
        goals: [
          'Maintain consistent food quality',
          'Optimize kitchen efficiency',
          'Reduce ingredient waste',
          'Streamline menu updates'
        ],
        demographics: {
          age: '30-40',
          experience: '12+ years culinary',
          techSavvy: 'Low-Moderate'
        }
      }
    ];

    setPersonas(mockPersonas);
    setIsProcessing(false);
    generateFeatures();
  };

  const generateFeatures = async () => {
    setIsProcessing(true);
    setCurrentStep('features');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockFeatures: Feature[] = [
      {
        name: 'Smart Reservation System',
        description: 'AI-powered table management with real-time availability',
        reach: 85,
        impact: 9,
        confidence: 8,
        effort: 5,
        score: Math.round((85 * 9 * 8) / 5)
      },
      {
        name: 'Digital Menu Management',
        description: 'Dynamic menu updates with ingredient tracking',
        reach: 70,
        impact: 8,
        confidence: 9,
        effort: 4,
        score: Math.round((70 * 8 * 9) / 4)
      },
      {
        name: 'Analytics Dashboard',
        description: 'Real-time insights on operations and performance',
        reach: 60,
        impact: 7,
        confidence: 8,
        effort: 6,
        score: Math.round((60 * 7 * 8) / 6)
      },
      {
        name: 'Staff Scheduling',
        description: 'Automated scheduling with availability management',
        reach: 75,
        impact: 6,
        confidence: 7,
        effort: 4,
        score: Math.round((75 * 6 * 7) / 4)
      },
      {
        name: 'Customer Feedback Hub',
        description: 'Integrated review management and response system',
        reach: 50,
        impact: 5,
        confidence: 9,
        effort: 3,
        score: Math.round((50 * 5 * 9) / 3)
      }
    ].sort((a, b) => b.score - a.score);

    setFeatures(mockFeatures);
    setIsProcessing(false);
    generateOnboarding();
  };

  const generateOnboarding = async () => {
    setIsProcessing(true);
    setCurrentStep('onboarding');
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockOnboarding: OnboardingStep[] = [
      {
        step: 1,
        title: 'Welcome & Video Introduction',
        description: 'Personalized welcome email with 2-minute product demo video',
        wireframe: 'Welcome screen with play button and restaurant setup checklist'
      },
      {
        step: 2,
        title: 'Restaurant Profile Setup',
        description: 'Basic information: name, cuisine type, seating capacity, hours',
        wireframe: 'Form with restaurant details and photo upload'
      },
      {
        step: 3,
        title: 'Interactive Feature Tour',
        description: 'Guided walkthrough of reservation system, menu management, and analytics',
        wireframe: 'Dashboard with highlighted tooltips and progress indicators'
      },
      {
        step: 4,
        title: 'Create First Menu Item',
        description: 'Add signature dish with pricing, ingredients, and dietary information',
        wireframe: 'Menu builder interface with drag-and-drop functionality'
      },
      {
        step: 5,
        title: 'Go Live & Share',
        description: 'Activate reservation system and generate shareable booking link',
        wireframe: 'Success screen with QR code and social sharing options'
      }
    ];

    setOnboardingFlow(mockOnboarding);
    setIsProcessing(false);
    generateWireframes();
  };

  const generateWireframes = async () => {
    setIsProcessing(true);
    setCurrentStep('wireframes');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const mockWireframes: WireframeScreen[] = [
      {
        id: 'login',
        name: 'Login Screen',
        description: 'Simple authentication with email/password and social login options',
        elements: [
          'Restaurant logo placeholder',
          'Email input field',
          'Password input field',
          'Remember me checkbox',
          'Login button (primary)',
          'Forgot password link',
          'Google/Facebook login buttons',
          'Sign up link'
        ]
      },
      {
        id: 'dashboard',
        name: 'Main Dashboard',
        description: 'Overview of key metrics, reservations, and quick actions',
        elements: [
          'Header with restaurant name and user menu',
          'Today\'s stats cards (reservations, revenue, capacity)',
          'Real-time reservation timeline',
          'Quick action buttons (new reservation, menu update)',
          'Recent activity feed',
          'Navigation sidebar',
          'Notification bell with badge'
        ]
      },
      {
        id: 'reservations',
        name: 'Reservation Management',
        description: 'Table layout with real-time availability and booking interface',
        elements: [
          'Interactive floor plan with table status',
          'Date/time picker',
          'Available time slots grid',
          'Guest information form',
          'Special requests text area',
          'Confirmation details panel',
          'Waitlist management section'
        ]
      },
      {
        id: 'menu',
        name: 'Menu Management',
        description: 'Digital menu builder with categories and item management',
        elements: [
          'Category tabs (appetizers, mains, desserts)',
          'Menu item cards with photos',
          'Add new item button',
          'Price and description fields',
          'Ingredient list with allergen tags',
          'Availability toggle switches',
          'Drag-and-drop reordering'
        ]
      }
    ];

    setWireframes(mockWireframes);
    setIsProcessing(false);
    setCurrentStep('complete');
  };

  const generateReport = () => {
    return `# Product Strategy Report

## Interview Summary
Conducted 3 LiveKit video interviews (5 minutes each) with target users:

${interviews.map(interview => `
### ${interview.persona} Interview
- **Duration:** ${Math.floor(interview.duration / 60)}:${(interview.duration % 60).toString().padStart(2, '0')}
- **Clip URL:** ${interview.clipUrl}
- **Key Insights:**
${interview.keyInsights.map(insight => `  - ${insight}`).join('\n')}
`).join('')}

## User Personas

${personas.map(persona => `
### ${persona.name} (${persona.role})
**Demographics:** ${persona.demographics.age}, ${persona.demographics.experience}, ${persona.demographics.techSavvy} tech-savvy

**Pain Points:**
${persona.painPoints.map(point => `- ${point}`).join('\n')}

**Goals:**
${persona.goals.map(goal => `- ${goal}`).join('\n')}
`).join('')}

## Feature Prioritization (RICE)

| Feature | Reach | Impact | Confidence | Effort | Score |
|---------|-------|--------|------------|--------|-------|
${features.map(feature => 
  `| ${feature.name} | ${feature.reach} | ${feature.impact} | ${feature.confidence} | ${feature.effort} | ${feature.score} |`
).join('\n')}

## 5-Step User Onboarding Flow

${onboardingFlow.map(step => `
${step.step}. **${step.title}**
   ${step.description}
`).join('')}

## Wireframes

${wireframes.map(wireframe => `
### ${wireframe.name}
${wireframe.description}

**Key Elements:**
${wireframe.elements.map(element => `- ${element}`).join('\n')}
`).join('')}

---
*Generated by Product Co-founder Agent | Total Interview Time: ${Math.floor(interviews.reduce((total, i) => total + i.duration, 0) / 60)} minutes*`;
  };

  const downloadReport = () => {
    const report = generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'product-strategy-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const startScreenShare = () => {
    setIsScreenSharing(true);
    // Simulate screen sharing for wireframe presentation
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Rocket className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Product Co-founder Agent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Validate user needs and define product scope
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-2 mb-8 overflow-x-auto">
        {[
          { id: 'intro', label: 'Introduction', icon: Users },
          { id: 'interviews', label: 'Interviews', icon: Video },
          { id: 'personas', label: 'Personas', icon: Users },
          { id: 'features', label: 'Features', icon: Star },
          { id: 'onboarding', label: 'Onboarding', icon: Target },
          { id: 'wireframes', label: 'Wireframes', icon: Monitor },
          { id: 'complete', label: 'Report', icon: FileText }
        ].map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs ${
              currentStep === step.id 
                ? 'bg-purple-600 text-white' 
                : index < ['intro', 'interviews', 'personas', 'features', 'onboarding', 'wireframes', 'complete'].indexOf(currentStep)
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
            }`}>
              <step.icon size={12} />
            </div>
            <span className="ml-1 text-xs font-medium text-gray-700 dark:text-gray-300 hidden sm:inline">
              {step.label}
            </span>
            {index < 6 && (
              <div className={`w-4 h-0.5 mx-2 ${
                index < ['intro', 'interviews', 'personas', 'features', 'onboarding', 'wireframes', 'complete'].indexOf(currentStep)
                  ? 'bg-green-500'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Let's Validate Your Product Idea
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              I'll conduct three 5-minute LiveKit video interviews with sample users, then create personas, prioritize features using RICE scoring, map onboarding flow, and share wireframes via screen-share.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {interviews.map((interview) => (
                <div key={interview.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    Interview {interview.id}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Target: {interview.persona}
                  </p>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      interview.status === 'completed' ? 'bg-green-500' :
                      interview.status === 'active' ? 'bg-blue-500' : 'bg-gray-400'
                    }`} />
                    <span className="text-xs text-gray-500 capitalize">{interview.status}</span>
                  </div>
                </div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => startInterview(interviews[0])}
              className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-lg font-semibold text-lg transition-all"
            >
              <Video size={20} />
              Start First Interview
            </motion.button>
          </motion.div>
        )}

        {currentStep === 'interviews' && currentInterview && (
          <motion.div
            key="interviews"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg"
          >
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <Video className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Live Interview: {currentInterview.persona}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Duration: {Math.floor(currentInterview.duration / 60)}:{(currentInterview.duration % 60).toString().padStart(2, '0')} / 5:00
              </p>
            </div>

            {/* Video Interface */}
            <div className="bg-gray-900 rounded-lg p-6 mb-6 aspect-video relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white">
                  <Video className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Interview in Progress</p>
                  <p className="text-sm opacity-75">Discussing pain points and user needs</p>
                </div>
              </div>
              
              {/* Video Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Mic size={16} />
                </button>
                <button className="p-2 bg-gray-800/80 text-white rounded-full hover:bg-gray-700/80">
                  <Video size={16} />
                </button>
                <button className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600">
                  <PhoneOff size={16} />
                </button>
              </div>
            </div>

            {/* Interview Progress */}
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                <span>Interview Progress</span>
                <span>{Math.round((currentInterview.duration / 300) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${Math.min((currentInterview.duration / 300) * 100, 100)}%` }}
                />
              </div>
            </div>

            {/* Current Questions */}
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-purple-900 dark:text-purple-200 mb-2">Current Discussion</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                "Can you walk me through your current process for managing reservations? What are the biggest pain points you face?"
              </p>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => completeInterview(currentInterview)}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium transition-colors"
              >
                <SkipForward size={16} />
                Complete Interview
              </button>
              <button className="px-4 py-3 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <Pause size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {currentStep === 'personas' && (
          <motion.div
            key="personas"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Analyzing Interview Data...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating detailed user personas from interview insights
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    User Personas
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Based on {interviews.length} video interviews
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {personas.map((persona, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="text-center mb-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Users className="w-8 h-8 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {persona.name}
                        </h3>
                        <p className="text-purple-600 dark:text-purple-400 font-medium">
                          {persona.role}
                        </p>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Demographics</h4>
                          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            <p>Age: {persona.demographics.age}</p>
                            <p>Experience: {persona.demographics.experience}</p>
                            <p>Tech Level: {persona.demographics.techSavvy}</p>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Pain Points</h4>
                          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            {persona.painPoints.slice(0, 2).map((point, i) => (
                              <li key={i} className="flex items-start gap-2">
                                <div className="w-1 h-1 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                                {point}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Goals</h4>
                          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            {persona.goals.slice(0, 2).map((goal, i) => (
                              <li key={i} className="flex items-start gap-2">
                                <div className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                                {goal}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'features' && (
          <motion.div
            key="features"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Prioritizing Features...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Applying RICE scoring methodology
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Feature Prioritization (RICE)
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Reach × Impact × Confidence ÷ Effort
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Feature</th>
                        <th className="text-center py-3 px-2 font-medium text-gray-900 dark:text-white">R</th>
                        <th className="text-center py-3 px-2 font-medium text-gray-900 dark:text-white">I</th>
                        <th className="text-center py-3 px-2 font-medium text-gray-900 dark:text-white">C</th>
                        <th className="text-center py-3 px-2 font-medium text-gray-900 dark:text-white">E</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900 dark:text-white">Score</th>
                      </tr>
                    </thead>
                    <tbody>
                      {features.map((feature, index) => (
                        <motion.tr
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="border-b border-gray-100 dark:border-gray-700"
                        >
                          <td className="py-4 px-4">
                            <div>
                              <h4 className="font-medium text-gray-900 dark:text-white">
                                {feature.name}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {feature.description}
                              </p>
                            </div>
                          </td>
                          <td className="text-center py-4 px-2 text-gray-900 dark:text-white">
                            {feature.reach}
                          </td>
                          <td className="text-center py-4 px-2 text-gray-900 dark:text-white">
                            {feature.impact}
                          </td>
                          <td className="text-center py-4 px-2 text-gray-900 dark:text-white">
                            {feature.confidence}
                          </td>
                          <td className="text-center py-4 px-2 text-gray-900 dark:text-white">
                            {feature.effort}
                          </td>
                          <td className="text-center py-4 px-4">
                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                              index === 0 ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                              index === 1 ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' :
                              'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                            }`}>
                              {feature.score}
                            </div>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'onboarding' && (
          <motion.div
            key="onboarding"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Designing Onboarding Flow...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Creating optimal user journey
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    5-Step User Onboarding Flow
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Optimized for quick time-to-value
                  </p>
                </div>

                <div className="space-y-4">
                  {onboardingFlow.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center font-bold">
                          {step.step}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                            {step.title}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 mb-3">
                            {step.description}
                          </p>
                          {step.wireframe && (
                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                <strong>Wireframe:</strong> {step.wireframe}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'wireframes' && (
          <motion.div
            key="wireframes"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {isProcessing ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Creating Wireframes...
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Designing key user interfaces
                </p>
              </div>
            ) : (
              <>
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Basic Wireframes
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Key screens for MVP development
                  </p>
                  
                  <div className="flex items-center justify-center gap-4 mt-4">
                    <button
                      onClick={startScreenShare}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                        isScreenSharing
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      <Share2 size={16} />
                      {isScreenSharing ? 'Sharing Screen' : 'Start Screen Share'}
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {wireframes.map((wireframe, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                    >
                      <div className="flex items-center gap-3 mb-4">
                        <Monitor className="w-6 h-6 text-purple-500" />
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                          {wireframe.name}
                        </h3>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {wireframe.description}
                      </p>

                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4 min-h-32">
                        <div className="text-center text-gray-500 dark:text-gray-400">
                          <Monitor className="w-12 h-12 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">Wireframe Preview</p>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Key Elements:</h4>
                        <ul className="space-y-1">
                          {wireframe.elements.slice(0, 4).map((element, i) => (
                            <li key={i} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                              <div className="w-1 h-1 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                              {element}
                            </li>
                          ))}
                          {wireframe.elements.length > 4 && (
                            <li className="text-sm text-gray-500 dark:text-gray-400">
                              +{wireframe.elements.length - 4} more elements
                            </li>
                          )}
                        </ul>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </>
            )}
          </motion.div>
        )}

        {currentStep === 'complete' && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Product Strategy Complete
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Your comprehensive product validation report is ready
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Video className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{interviews.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Interviews</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Users className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{personas.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Personas</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{features.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Features</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg">
                <Monitor className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{wireframes.length}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Wireframes</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={downloadReport}
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-lg font-medium transition-all"
              >
                <Download size={16} />
                Download Full Report
              </motion.button>
              <button
                onClick={() => {
                  setCurrentStep('intro');
                  setInterviews(prev => prev.map(i => ({ ...i, status: 'pending', duration: 0, keyInsights: [] })));
                  setPersonas([]);
                  setFeatures([]);
                  setOnboardingFlow([]);
                  setWireframes([]);
                }}
                className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
              >
                New Analysis
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};