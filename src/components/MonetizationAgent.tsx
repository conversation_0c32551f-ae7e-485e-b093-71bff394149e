import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Crown,
  Building2,
  Zap,
  Calculator,
  FileText,
  Download,
  BarChart3,
  Target,
  Handshake,
  Shield,
  Clock,
  CheckCircle,
  Star,
  Globe,
  Headphones,
  Settings,
  Rocket
} from 'lucide-react';

interface PricingTier {
  name: string;
  price: number;
  billing: 'monthly' | 'annual';
  description: string;
  features: string[];
  limits: {
    agents: number;
    sessions: number;
    projects: number;
    storage: string;
    support: string;
  };
  targetAudience: string;
  popular?: boolean;
}

interface UsageBilling {
  metric: string;
  unit: string;
  price: number;
  description: string;
  tiers: Array<{
    from: number;
    to: number | null;
    price: number;
  }>;
}

interface EnterprisePackage {
  feature: string;
  description: string;
  sla: string;
  pricing: string;
  included: boolean;
}

interface RevenueShare {
  partner: string;
  model: string;
  percentage: number;
  description: string;
  terms: string[];
  benefits: string[];
}

export const MonetizationAgent: React.FC = () => {
  const [currentView, setCurrentView] = useState<'overview' | 'tiers' | 'usage' | 'enterprise' | 'partnerships'>('overview');
  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>([]);
  const [usageBilling, setUsageBilling] = useState<UsageBilling[]>([]);
  const [enterprisePackage, setEnterprisePackage] = useState<EnterprisePackage[]>([]);
  const [revenueShares, setRevenueShares] = useState<RevenueShare[]>([]);
  const [projectedRevenue, setProjectedRevenue] = useState({
    year1: 0,
    year2: 0,
    year3: 0
  });

  useEffect(() => {
    initializeMonetizationData();
  }, []);

  const initializeMonetizationData = () => {
    // SaaS Pricing Tiers
    const tiers: PricingTier[] = [
      {
        name: 'Starter',
        price: 29,
        billing: 'monthly',
        description: 'Perfect for solo entrepreneurs and early-stage startups',
        features: [
          '2 AI Co-founder Agents (Strategic + Product)',
          '5 LiveKit sessions per month',
          '3 projects with basic analytics',
          'Standard email support',
          'Basic integrations (Slack, GitHub)',
          'Community access',
          'Standard templates'
        ],
        limits: {
          agents: 2,
          sessions: 5,
          projects: 3,
          storage: '1GB',
          support: 'Email (48h response)'
        },
        targetAudience: 'Solo entrepreneurs, early-stage startups'
      },
      {
        name: 'Professional',
        price: 99,
        billing: 'monthly',
        description: 'Comprehensive solution for growing startups and small teams',
        features: [
          'All 5 AI Co-founder Agents',
          '25 LiveKit sessions per month',
          'Unlimited projects with advanced analytics',
          'Priority email + chat support',
          'Full Composio integration suite',
          'Custom agent training',
          'Advanced templates & workflows',
          'Team collaboration features',
          'API access (1000 calls/month)'
        ],
        limits: {
          agents: 5,
          sessions: 25,
          projects: -1, // unlimited
          storage: '10GB',
          support: 'Email + Chat (24h response)'
        },
        targetAudience: 'Growing startups, small teams',
        popular: true
      },
      {
        name: 'Enterprise',
        price: 299,
        billing: 'monthly',
        description: 'Full-scale solution for established companies and agencies',
        features: [
          'All 5 AI Co-founder Agents + Custom Agents',
          'Unlimited LiveKit sessions',
          'Unlimited projects with enterprise analytics',
          '24/7 phone + dedicated support',
          'White-label customization',
          'Advanced security & compliance',
          'Custom integrations & workflows',
          'Multi-team management',
          'Unlimited API access',
          'On-premise deployment option',
          'Custom SLAs & training'
        ],
        limits: {
          agents: -1, // unlimited + custom
          sessions: -1, // unlimited
          projects: -1, // unlimited
          storage: '100GB',
          support: '24/7 Phone + Dedicated CSM'
        },
        targetAudience: 'Established companies, agencies, consultants'
      }
    ];

    // Usage-Based Billing Models
    const usage: UsageBilling[] = [
      {
        metric: 'LiveKit Sessions',
        unit: 'per session',
        price: 2.50,
        description: 'Additional video/voice sessions beyond plan limits',
        tiers: [
          { from: 1, to: 50, price: 2.50 },
          { from: 51, to: 200, price: 2.00 },
          { from: 201, to: null, price: 1.50 }
        ]
      },
      {
        metric: 'AI Agent Hours',
        unit: 'per hour',
        price: 0.75,
        description: 'Extended agent processing time for complex analyses',
        tiers: [
          { from: 1, to: 100, price: 0.75 },
          { from: 101, to: 500, price: 0.60 },
          { from: 501, to: null, price: 0.45 }
        ]
      },
      {
        metric: 'Custom Agent Training',
        unit: 'per training session',
        price: 25.00,
        description: 'Training custom agents with company-specific data',
        tiers: [
          { from: 1, to: 10, price: 25.00 },
          { from: 11, to: 50, price: 20.00 },
          { from: 51, to: null, price: 15.00 }
        ]
      },
      {
        metric: 'API Calls',
        unit: 'per 1000 calls',
        price: 5.00,
        description: 'Additional API usage beyond plan allocations',
        tiers: [
          { from: 1, to: 100, price: 5.00 },
          { from: 101, to: 1000, price: 4.00 },
          { from: 1001, to: null, price: 3.00 }
        ]
      },
      {
        metric: 'Storage',
        unit: 'per GB/month',
        price: 0.50,
        description: 'Additional storage for projects and session recordings',
        tiers: [
          { from: 1, to: 100, price: 0.50 },
          { from: 101, to: 1000, price: 0.40 },
          { from: 1001, to: null, price: 0.30 }
        ]
      }
    ];

    // Enterprise White-Label Package
    const enterprise: EnterprisePackage[] = [
      {
        feature: 'Custom Branding',
        description: 'Full white-label customization with your company branding',
        sla: '99.9% uptime guarantee',
        pricing: 'Included in Enterprise tier',
        included: true
      },
      {
        feature: 'Dedicated Infrastructure',
        description: 'Isolated cloud environment or on-premise deployment',
        sla: '99.95% uptime with 4-hour response',
        pricing: '+$500/month for dedicated cloud',
        included: false
      },
      {
        feature: 'Custom Agent Development',
        description: 'Bespoke AI agents tailored to your industry and processes',
        sla: '30-day delivery for standard agents',
        pricing: '$5,000 per custom agent',
        included: false
      },
      {
        feature: 'Advanced Security & Compliance',
        description: 'SOC 2 Type II, GDPR, HIPAA compliance with audit trails',
        sla: 'Quarterly security reviews',
        pricing: 'Included in Enterprise tier',
        included: true
      },
      {
        feature: 'Dedicated Customer Success Manager',
        description: '24/7 dedicated support with named CSM and technical account manager',
        sla: '1-hour response for critical issues',
        pricing: 'Included in Enterprise tier',
        included: true
      },
      {
        feature: 'Custom Integrations',
        description: 'Bespoke integrations with your existing tools and workflows',
        sla: '45-day delivery for standard integrations',
        pricing: '$2,500 per custom integration',
        included: false
      },
      {
        feature: 'Training & Onboarding',
        description: 'Comprehensive team training and change management support',
        sla: '2-week onboarding completion',
        pricing: '$3,000 one-time fee',
        included: false
      },
      {
        feature: 'Advanced Analytics & Reporting',
        description: 'Custom dashboards, usage analytics, and ROI reporting',
        sla: 'Monthly business reviews',
        pricing: 'Included in Enterprise tier',
        included: true
      }
    ];

    // Revenue Share Models
    const partnerships: RevenueShare[] = [
      {
        partner: 'Bolt.new (StackBlitz)',
        model: 'Revenue Share + Strategic Partnership',
        percentage: 15,
        description: 'Revenue sharing for projects deployed through Bolt.new integration',
        terms: [
          '15% of revenue from users who deploy via Bolt.new',
          'Minimum $10,000 monthly revenue share',
          'Joint marketing and co-selling opportunities',
          'Preferred integration partner status',
          'Quarterly business reviews and roadmap alignment'
        ],
        benefits: [
          'Access to Bolt.new\'s 2M+ developer user base',
          'Featured placement in Bolt.new marketplace',
          'Joint webinars and conference presentations',
          'Technical integration support and priority API access',
          'Co-branded marketing materials and case studies'
        ]
      },
      {
        partner: 'LiveKit',
        model: 'Technology Partnership + Usage Revenue Share',
        percentage: 8,
        description: 'Revenue sharing based on LiveKit usage within our platform',
        terms: [
          '8% of revenue from LiveKit-enabled features',
          'Volume discounts on LiveKit infrastructure costs',
          'Joint technical roadmap development',
          'Priority support and feature requests',
          'Annual partnership review and optimization'
        ],
        benefits: [
          'Reduced infrastructure costs (30-40% savings)',
          'Early access to new LiveKit features',
          'Joint case studies and technical content',
          'Conference speaking opportunities',
          'Technical advisory board participation'
        ]
      },
      {
        partner: 'Composio',
        model: 'Integration Revenue Share',
        percentage: 12,
        description: 'Revenue sharing for customers using Composio integrations',
        terms: [
          '12% of revenue from integration-heavy customers',
          'Tiered pricing based on integration usage',
          'Joint customer success initiatives',
          'Shared technical documentation and support',
          'Bi-annual partnership optimization reviews'
        ],
        benefits: [
          'Access to 200+ pre-built integrations',
          'Reduced integration development costs',
          'Joint customer onboarding programs',
          'Shared marketing and lead generation',
          'Priority integration development requests'
        ]
      }
    ];

    setPricingTiers(tiers);
    setUsageBilling(usage);
    setEnterprisePackage(enterprise);
    setRevenueShares(partnerships);

    // Calculate projected revenue
    calculateProjectedRevenue(tiers);
  };

  const calculateProjectedRevenue = (tiers: PricingTier[]) => {
    // Conservative projections based on SaaS industry benchmarks
    const projections = {
      year1: {
        starter: { customers: 500, mrr: 29 },
        professional: { customers: 150, mrr: 99 },
        enterprise: { customers: 25, mrr: 299 }
      },
      year2: {
        starter: { customers: 1200, mrr: 29 },
        professional: { customers: 400, mrr: 99 },
        enterprise: { customers: 75, mrr: 299 }
      },
      year3: {
        starter: { customers: 2000, mrr: 29 },
        professional: { customers: 800, mrr: 99 },
        enterprise: { customers: 150, mrr: 299 }
      }
    };

    const year1Revenue = Object.values(projections.year1).reduce((sum, tier) => 
      sum + (tier.customers * tier.mrr * 12), 0
    );
    const year2Revenue = Object.values(projections.year2).reduce((sum, tier) => 
      sum + (tier.customers * tier.mrr * 12), 0
    );
    const year3Revenue = Object.values(projections.year3).reduce((sum, tier) => 
      sum + (tier.customers * tier.mrr * 12), 0
    );

    setProjectedRevenue({
      year1: year1Revenue,
      year2: year2Revenue,
      year3: year3Revenue
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const generateMonetizationReport = () => {
    return `# Ultimate Startup Co-founder - Monetization Strategy

## Executive Summary

This comprehensive monetization strategy outlines pricing tiers, usage-based billing, enterprise packages, and strategic partnerships for the Ultimate Startup Co-founder platform.

## SaaS Pricing Tiers

${pricingTiers.map(tier => `
### ${tier.name} - ${formatCurrency(tier.price)}/${tier.billing}

**Target Audience:** ${tier.targetAudience}
**Description:** ${tier.description}

**Features:**
${tier.features.map(feature => `- ${feature}`).join('\n')}

**Limits:**
- AI Agents: ${tier.limits.agents === -1 ? 'Unlimited' : tier.limits.agents}
- LiveKit Sessions: ${tier.limits.sessions === -1 ? 'Unlimited' : `${tier.limits.sessions}/month`}
- Projects: ${tier.limits.projects === -1 ? 'Unlimited' : tier.limits.projects}
- Storage: ${tier.limits.storage}
- Support: ${tier.limits.support}
`).join('')}

## Usage-Based Billing Model

${usageBilling.map(usage => `
### ${usage.metric}
**Base Price:** ${formatCurrency(usage.price)} ${usage.unit}
**Description:** ${usage.description}

**Volume Tiers:**
${usage.tiers.map(tier => 
  `- ${tier.from}${tier.to ? `-${tier.to}` : '+'} units: ${formatCurrency(tier.price)} ${usage.unit}`
).join('\n')}
`).join('')}

## Enterprise White-Label Package

${enterprisePackage.map(item => `
### ${item.feature}
**Description:** ${item.description}
**SLA:** ${item.sla}
**Pricing:** ${item.pricing}
**Included:** ${item.included ? 'Yes' : 'Additional cost'}
`).join('')}

## Revenue Share Partnerships

${revenueShares.map(partnership => `
### ${partnership.partner}
**Model:** ${partnership.model}
**Revenue Share:** ${partnership.percentage}%
**Description:** ${partnership.description}

**Terms:**
${partnership.terms.map(term => `- ${term}`).join('\n')}

**Benefits:**
${partnership.benefits.map(benefit => `- ${benefit}`).join('\n')}
`).join('')}

## Revenue Projections

| Year | Projected Revenue | Growth Rate |
|------|------------------|-------------|
| 1    | ${formatCurrency(projectedRevenue.year1)} | - |
| 2    | ${formatCurrency(projectedRevenue.year2)} | ${Math.round(((projectedRevenue.year2 - projectedRevenue.year1) / projectedRevenue.year1) * 100)}% |
| 3    | ${formatCurrency(projectedRevenue.year3)} | ${Math.round(((projectedRevenue.year3 - projectedRevenue.year2) / projectedRevenue.year2) * 100)}% |

## Key Monetization Strategies

1. **Freemium to Premium Conversion**
   - 14-day free trial for all tiers
   - Usage-based upselling opportunities
   - Feature-gated progression path

2. **Enterprise Value Proposition**
   - White-label customization for agencies
   - Dedicated infrastructure and support
   - Custom agent development services

3. **Strategic Partnerships**
   - Revenue sharing with key technology partners
   - Joint go-to-market strategies
   - Integrated ecosystem approach

4. **Usage-Based Growth**
   - Transparent overage pricing
   - Volume discounts for high-usage customers
   - Predictable scaling costs

---
*Generated by MonetizationAgent | Ultimate Startup Co-founder Platform*`;
  };

  const downloadReport = () => {
    const report = generateMonetizationReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'monetization-strategy-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <DollarSign className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          MonetizationAgent
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Comprehensive pricing strategy and revenue optimization
        </p>
      </div>

      {/* Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
        <div className="flex flex-wrap gap-2">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'tiers', label: 'Pricing Tiers', icon: Crown },
            { id: 'usage', label: 'Usage Billing', icon: Calculator },
            { id: 'enterprise', label: 'Enterprise', icon: Building2 },
            { id: 'partnerships', label: 'Partnerships', icon: Handshake }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setCurrentView(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                currentView === tab.id
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <tab.icon size={16} />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentView === 'overview' && (
          <motion.div
            key="overview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Revenue Projections */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Revenue Projections</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-3" />
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {formatCurrency(projectedRevenue.year1)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Year 1 Revenue</p>
                </div>
                <div className="text-center p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <Target className="w-8 h-8 text-blue-500 mx-auto mb-3" />
                  <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    {formatCurrency(projectedRevenue.year2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Year 2 Revenue</p>
                </div>
                <div className="text-center p-6 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <Rocket className="w-8 h-8 text-purple-500 mx-auto mb-3" />
                  <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    {formatCurrency(projectedRevenue.year3)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Year 3 Revenue</p>
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg text-center">
                <Users className="w-6 h-6 text-green-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">3</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pricing Tiers</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg text-center">
                <Calculator className="w-6 h-6 text-blue-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">5</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Usage Metrics</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg text-center">
                <Building2 className="w-6 h-6 text-purple-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">8</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Enterprise Features</p>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg text-center">
                <Handshake className="w-6 h-6 text-orange-500 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900 dark:text-white">3</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Revenue Partners</p>
              </div>
            </div>

            {/* Strategy Overview */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Monetization Strategy</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Freemium to Premium</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">14-day free trial with feature-gated progression</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Usage-Based Scaling</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Transparent overage pricing with volume discounts</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Enterprise Value</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">White-label solutions with dedicated support</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Strategic Partnerships</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Revenue sharing with key technology partners</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {currentView === 'tiers' && (
          <motion.div
            key="tiers"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">SaaS Pricing Tiers</h2>
              <p className="text-gray-600 dark:text-gray-400">Choose the perfect plan for your startup journey</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {pricingTiers.map((tier, index) => (
                <motion.div
                  key={tier.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg relative ${
                    tier.popular ? 'ring-2 ring-green-500 scale-105' : ''
                  }`}
                >
                  {tier.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{tier.name}</h3>
                    <div className="flex items-baseline justify-center gap-1">
                      <span className="text-3xl font-bold text-gray-900 dark:text-white">
                        {formatCurrency(tier.price)}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">/{tier.billing}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">{tier.description}</p>
                  </div>

                  <div className="space-y-3 mb-6">
                    {tier.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mb-6">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">Plan Limits</h4>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex justify-between">
                        <span>AI Agents:</span>
                        <span>{tier.limits.agents === -1 ? 'Unlimited' : tier.limits.agents}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sessions:</span>
                        <span>{tier.limits.sessions === -1 ? 'Unlimited' : `${tier.limits.sessions}/mo`}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Projects:</span>
                        <span>{tier.limits.projects === -1 ? 'Unlimited' : tier.limits.projects}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Storage:</span>
                        <span>{tier.limits.storage}</span>
                      </div>
                    </div>
                  </div>

                  <button className={`w-full py-3 rounded-lg font-medium transition-colors ${
                    tier.popular
                      ? 'bg-green-500 hover:bg-green-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
                  }`}>
                    Get Started
                  </button>

                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
                    Target: {tier.targetAudience}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {currentView === 'usage' && (
          <motion.div
            key="usage"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Usage-Based Billing</h2>
              <p className="text-gray-600 dark:text-gray-400">Transparent pricing that scales with your usage</p>
            </div>

            <div className="space-y-6">
              {usageBilling.map((usage, index) => (
                <motion.div
                  key={usage.metric}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{usage.metric}</h3>
                      <p className="text-gray-600 dark:text-gray-400">{usage.description}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {formatCurrency(usage.price)}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{usage.unit}</p>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">Volume Pricing Tiers</h4>
                    <div className="space-y-2">
                      {usage.tiers.map((tier, tierIndex) => (
                        <div key={tierIndex} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {tier.from}{tier.to ? `-${tier.to}` : '+'} units
                          </span>
                          <span className="font-medium text-gray-900 dark:text-white">
                            {formatCurrency(tier.price)} {usage.unit}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {currentView === 'enterprise' && (
          <motion.div
            key="enterprise"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Enterprise White-Label Package</h2>
              <p className="text-gray-600 dark:text-gray-400">Comprehensive enterprise solutions with SLAs and dedicated support</p>
            </div>

            <div className="space-y-4">
              {enterprisePackage.map((item, index) => (
                <motion.div
                  key={item.feature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        item.included ? 'bg-green-100 dark:bg-green-900/30' : 'bg-orange-100 dark:bg-orange-900/30'
                      }`}>
                        {item.included ? (
                          <CheckCircle className="w-5 h-5 text-green-600" />
                        ) : (
                          <DollarSign className="w-5 h-5 text-orange-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{item.feature}</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-3">{item.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center gap-2">
                            <Shield className="w-4 h-4 text-blue-500" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">SLA: {item.sla}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <DollarSign className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{item.pricing}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                      item.included 
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        : 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
                    }`}>
                      {item.included ? 'Included' : 'Add-on'}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Enterprise Summary */}
            <div className="bg-gradient-to-r from-purple-500 to-blue-600 rounded-xl p-6 text-white">
              <h3 className="text-xl font-bold mb-4">Enterprise Package Benefits</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <Headphones className="w-8 h-8 mx-auto mb-2 opacity-80" />
                  <p className="font-medium">24/7 Support</p>
                  <p className="text-sm opacity-80">Dedicated CSM</p>
                </div>
                <div className="text-center">
                  <Settings className="w-8 h-8 mx-auto mb-2 opacity-80" />
                  <p className="font-medium">Custom Setup</p>
                  <p className="text-sm opacity-80">White-label ready</p>
                </div>
                <div className="text-center">
                  <Shield className="w-8 h-8 mx-auto mb-2 opacity-80" />
                  <p className="font-medium">Enterprise Security</p>
                  <p className="text-sm opacity-80">SOC 2 compliant</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {currentView === 'partnerships' && (
          <motion.div
            key="partnerships"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Revenue Share Partnerships</h2>
              <p className="text-gray-600 dark:text-gray-400">Strategic partnerships with revenue sharing models</p>
            </div>

            <div className="space-y-6">
              {revenueShares.map((partnership, index) => (
                <motion.div
                  key={partnership.partner}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
                >
                  <div className="flex items-start justify-between mb-6">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{partnership.partner}</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-2">{partnership.description}</p>
                      <div className="flex items-center gap-4">
                        <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm rounded-full">
                          {partnership.model}
                        </span>
                        <span className="text-lg font-bold text-green-600 dark:text-green-400">
                          {partnership.percentage}% Revenue Share
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Partnership Terms</h4>
                      <div className="space-y-2">
                        {partnership.terms.map((term, termIndex) => (
                          <div key={termIndex} className="flex items-start gap-2">
                            <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{term}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Partnership Benefits</h4>
                      <div className="space-y-2">
                        {partnership.benefits.map((benefit, benefitIndex) => (
                          <div key={benefitIndex} className="flex items-start gap-2">
                            <Star className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Download Report */}
      <div className="flex gap-4">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={downloadReport}
          className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-medium transition-all"
        >
          <Download size={16} />
          Download Monetization Report
        </motion.button>
        <button
          onClick={() => setCurrentView('overview')}
          className="px-6 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors"
        >
          Reset View
        </button>
      </div>
    </div>
  );
};