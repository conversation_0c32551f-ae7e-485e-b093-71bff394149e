import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  Mic, 
  Video, 
  Send, 
  TrendingUp, 
  Rocket, 
  Code2, 
  Settings, 
  Megaphone,
  Play,
  ArrowRight,
  Sparkles,
  Users,
  Zap,
  FileText,
  DollarSign,
  Phone,
  ChevronRight,
  Star,
  Globe,
  Github,
  Twitter,
  Linkedin,
  CheckCircle,
  Clock,
  Target,
  LogOut,
  User,
  Monitor,
  X
} from 'lucide-react';
import { useAIOrchestrator } from '../hooks/useAIOrchestrator';
import { LiveKitRoom } from './LiveKitRoom';
import { useAuth } from './AuthProvider';
import { Link } from 'react-router-dom';

interface Agent {
  id: string;
  name: string;
  role: string;
  icon: React.ElementType;
  color: string;
  description: string;
  expertise: string[];
}

interface HomepageProps {
  onShowAuth: () => void;
}

export const Homepage: React.FC<HomepageProps> = ({ onShowAuth }) => {
  const [chatInput, setChatInput] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [showLiveKitModal, setShowLiveKitModal] = useState(false);
  const [currentAgentIndex, setCurrentAgentIndex] = useState(0);
  const { user, logout, isAuthenticated } = useAuth();

  const {
    messages,
    isProcessing,
    pendingSuggestions,
    liveKitSession,
    sendMessage,
    executeSuggestion,
    startLiveKitSession,
    endLiveKitSession,
    clearMessages
  } = useAIOrchestrator();

  const agents: Agent[] = [
    {
      id: 'strategic',
      name: 'Strategic',
      role: 'Market & Strategy',
      icon: TrendingUp,
      color: 'from-blue-500 to-purple-600',
      description: 'Market analysis, competitive research, and strategic planning',
      expertise: ['Market Research', 'Strategic Planning', 'Competitive Analysis', 'Business Modeling']
    },
    {
      id: 'product',
      name: 'Product',
      role: 'UX & Features',
      icon: Rocket,
      color: 'from-purple-500 to-pink-600',
      description: 'Product strategy, user experience, and feature prioritization',
      expertise: ['Product Strategy', 'UX Design', 'Feature Planning', 'User Research']
    },
    {
      id: 'technical',
      name: 'Technical',
      role: 'Architecture & Code',
      icon: Code2,
      color: 'from-green-500 to-blue-600',
      description: 'System architecture, development, and technical implementation',
      expertise: ['System Architecture', 'Code Generation', 'Technical Review', 'DevOps']
    },
    {
      id: 'operations',
      name: 'Operations',
      role: 'Process & Scale',
      icon: Settings,
      color: 'from-orange-500 to-red-600',
      description: 'Process optimization, resource management, and operations',
      expertise: ['Process Design', 'Resource Planning', 'Quality Assurance', 'Automation']
    },
    {
      id: 'marketing',
      name: 'Marketing',
      role: 'Growth & Brand',
      icon: Megaphone,
      color: 'from-pink-500 to-red-600',
      description: 'Growth strategy, brand building, and customer acquisition',
      expertise: ['Growth Strategy', 'Content Creation', 'Analytics', 'Campaign Management']
    }
  ];

  // Auto-rotate agent preview
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAgentIndex((prev) => (prev + 1) % agents.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;

    const agentIds = selectedAgent ? [selectedAgent] : undefined;
    await sendMessage(chatInput, agentIds);
    setChatInput('');
    
    // If user is not logged in and this is their first message, prompt them to create an account
    if (!isAuthenticated && messages.length === 0) {
      setTimeout(() => {
        onShowAuth();
      }, 2000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleStartLiveKit = async (sessionType: 'voice' | 'video' | 'screen-share') => {
    if (!isAuthenticated) {
      onShowAuth();
      return;
    }
    
    try {
      const agentIds = selectedAgent ? [selectedAgent] : ['strategic', 'product', 'technical'];
      await startLiveKitSession(agentIds, sessionType);
      setShowLiveKitModal(false);
    } catch (error) {
      console.error('Failed to start LiveKit session:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'medium':
        return 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'low':
        return 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Show LiveKit room if session is active
  if (liveKitSession && liveKitSession.status === 'connected') {
    return <LiveKitRoom session={liveKitSession} onEnd={endLiveKitSession} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Ultimate Co-founder</span>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="hidden md:flex items-center gap-8">
                <Link to="/documentation" className="text-gray-600 hover:text-gray-900 transition-colors">Documentation</Link>
                <Link to="/data-room" className="text-gray-600 hover:text-gray-900 transition-colors">Data Room</Link>
                <Link to="/api" className="text-gray-600 hover:text-gray-900 transition-colors">API</Link>
                <Link to="/integrations" className="text-gray-600 hover:text-gray-900 transition-colors">Integrations</Link>
                <Link to="/company" className="text-gray-600 hover:text-gray-900 transition-colors">Company</Link>
              </div>
              
              {/* User Menu */}
              {isAuthenticated ? (
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <User className="w-4 h-4" />
                    {user?.name}
                  </div>
                  <Link to="/dashboard" className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                    Dashboard
                  </Link>
                  <button
                    onClick={logout}
                    className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
                    title="Logout"
                  >
                    <LogOut className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <button
                  onClick={onShowAuth}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  Sign In
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Floating Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-20 h-20 bg-purple-200 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute top-40 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 animate-bounce"></div>
            <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-200 rounded-full opacity-25 animate-pulse"></div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="relative z-10"
          >
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Your AI Co-Founder Team,
              <br />
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                All in One Chat
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
              Ask or speak your idea—get strategy, product, tech, ops, and marketing guidance instantly. 
              Five specialized AI co-founders working together in perfect harmony.
            </p>

            {/* Central Chat Widget */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 mb-8 max-w-2xl mx-auto"
            >
              {/* Agent Indicator */}
              {selectedAgent ? (
                <div className="mb-4 flex items-center justify-center gap-2">
                  <div className={`w-6 h-6 bg-gradient-to-br ${agents.find(a => a.id === selectedAgent)?.color || 'from-purple-500 to-blue-600'} rounded-lg flex items-center justify-center`}>
                    {(() => {
                      const IconComponent = agents.find(a => a.id === selectedAgent)?.icon || MessageCircle;
                      return <IconComponent className="w-3 h-3 text-white" />;
                    })()}
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Chatting with {agents.find(a => a.id === selectedAgent)?.name || 'Agent'} Co-founder
                  </span>
                </div>
              ) : (
                <div className="mb-4 flex items-center justify-center gap-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <Users className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Chatting with All Co-founders
                  </span>
                </div>
              )}

              {/* Chat Messages */}
              <div className="h-80 overflow-y-auto mb-4 space-y-3">
                {messages.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="w-8 h-8 text-purple-600" />
                    </div>
                    <p className="text-gray-500 mb-2">Ready to help you build your startup</p>
                    <p className="text-sm text-gray-400">Try: "I want to build a restaurant management app"</p>
                  </div>
                ) : (
                  <>
                    {messages.map((message) => (
                      <div key={message.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div className={`max-w-xs p-3 rounded-lg ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}>
                            {message.type === 'agent' && (
                              <p className="text-xs font-medium text-purple-600 mb-1">{message.agent}</p>
                            )}
                            <p className="text-sm">{message.content}</p>
                          </div>
                        </motion.div>

                        {/* Agent Suggestions */}
                        {message.suggestions && message.suggestions.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="mt-3 space-y-2"
                          >
                            {message.suggestions.map((suggestion) => {
                              const IconComponent = suggestion.icon || Target;
                              return (
                                <motion.button
                                  key={suggestion.id}
                                  onClick={() => executeSuggestion(suggestion)}
                                  className={`w-full text-left p-3 border-2 rounded-lg transition-all hover:scale-[1.02] ${getPriorityColor(suggestion.priority)}`}
                                  whileHover={{ scale: 1.02 }}
                                  whileTap={{ scale: 0.98 }}
                                >
                                  <div className="flex items-start gap-3">
                                    <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center flex-shrink-0">
                                      <IconComponent className="w-4 h-4 text-gray-600" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center justify-between mb-1">
                                        <h4 className="font-medium text-sm">{suggestion.title}</h4>
                                        <div className="flex items-center gap-2">
                                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                            suggestion.priority === 'high' ? 'bg-red-100 text-red-700' :
                                            suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                                            'bg-green-100 text-green-700'
                                          }`}>
                                            {suggestion.priority}
                                          </span>
                                          <span className="text-xs text-gray-500 flex items-center gap-1">
                                            <Clock className="w-3 h-3" />
                                            {suggestion.estimatedTime}
                                          </span>
                                        </div>
                                      </div>
                                      <p className="text-xs opacity-80 mb-2">{suggestion.description}</p>
                                      <div className="flex items-center justify-between">
                                        <span className="text-xs font-medium">{suggestion.agentName} Agent</span>
                                        <ArrowRight className="w-3 h-3" />
                                      </div>
                                    </div>
                                  </div>
                                </motion.button>
                              );
                            })}
                          </motion.div>
                        )}
                      </div>
                    ))}
                    {isProcessing && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex justify-start"
                      >
                        <div className="bg-gray-100 p-3 rounded-lg">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </>
                )}
              </div>

              {/* Chat Input */}
              <div className="flex items-center gap-3">
                <div className="flex-1 relative">
                  <textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={selectedAgent 
                      ? `Ask ${agents.find(a => a.id === selectedAgent)?.name} about your startup idea...`
                      : "Ask all agents about your startup idea..."}
                    className="w-full p-3 pr-12 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={1}
                    disabled={isProcessing}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!chatInput.trim() || isProcessing}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-purple-600 hover:text-purple-700 disabled:text-gray-400 transition-colors"
                  >
                    <Send size={16} />
                  </button>
                </div>
                <button
                  onClick={() => {
                    if (isAuthenticated) {
                      setShowLiveKitModal(true);
                    } else {
                      onShowAuth();
                    }
                  }}
                  className="p-3 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white rounded-lg transition-all"
                >
                  <Mic size={16} />
                </button>
              </div>
            </motion.div>

            {/* Agent Selection */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mb-12"
            >
              <p className="text-gray-600 mb-6">Or refine with a specific co-founder:</p>
              <div className="flex flex-wrap justify-center gap-4">
                {selectedAgent && (
                  <button
                    onClick={() => setSelectedAgent(null)}
                    className="flex items-center gap-3 px-6 py-3 rounded-xl border-2 border-gray-200 bg-white hover:border-gray-300 hover:scale-105 transition-all"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <Users className="w-4 h-4 text-white" />
                    </div>
                    <span className="font-medium text-gray-900">All Co-founders</span>
                  </button>
                )}
                
                {agents.map((agent) => {
                  const IconComponent = agent.icon;
                  return (
                    <button
                      key={agent.id}
                      onClick={() => setSelectedAgent(selectedAgent === agent.id ? null : agent.id)}
                      className={`flex items-center gap-3 px-6 py-3 rounded-xl border-2 transition-all hover:scale-105 ${
                        selectedAgent === agent.id
                          ? 'border-purple-500 bg-purple-50'
                          : 'border-gray-200 bg-white hover:border-gray-300'
                      }`}
                    >
                      <div className={`w-8 h-8 bg-gradient-to-br ${agent.color} rounded-lg flex items-center justify-center`}>
                        <IconComponent className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium text-gray-900">{agent.name}</span>
                    </button>
                  );
                })}
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <button 
                onClick={() => {
                  if (messages.length === 0) {
                    setChatInput("I want to build a restaurant management app with AI-powered features");
                  }
                }}
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-semibold text-lg transition-all hover:scale-105 shadow-lg"
              >
                Try It Now - Free
              </button>
              <button 
                onClick={() => {
                  if (isAuthenticated) {
                    setShowLiveKitModal(true);
                  } else {
                    onShowAuth();
                  }
                }}
                className="px-8 py-4 bg-white border-2 border-gray-300 hover:border-gray-400 text-gray-900 rounded-xl font-semibold text-lg transition-all hover:scale-105 flex items-center gap-2"
              >
                <Video size={20} />
                Start Live Session
              </button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Three simple steps to get comprehensive startup guidance from your AI co-founder team
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: 'Tell us your idea',
                description: 'Type or speak your startup concept. Our AI agents listen and understand your vision.',
                icon: MessageCircle,
                color: 'from-blue-500 to-purple-600'
              },
              {
                step: '02',
                title: 'Get unified guidance',
                description: 'All five co-founders respond in one feed with strategic, product, technical, ops, and marketing insights.',
                icon: Users,
                color: 'from-purple-500 to-pink-600'
              },
              {
                step: '03',
                title: 'Fine-tune & export',
                description: 'Click into any agent for detailed analysis, custom outputs, and actionable next steps.',
                icon: FileText,
                color: 'from-pink-500 to-red-600'
              }
            ].map((step, index) => {
              const IconComponent = step.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  className="text-center"
                >
                  <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-sm font-bold text-purple-600 mb-2">{step.step}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Agent Preview Carousel */}
      <section id="agents" className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Your AI Co-Founder Team</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Five specialized agents, each an expert in their domain, working together to build your startup
            </p>
          </motion.div>

          {/* Featured Agent */}
          <motion.div
            key={currentAgentIndex}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 mb-12 max-w-2xl mx-auto"
          >
            <div className="text-center">
              <div className={`w-20 h-20 bg-gradient-to-br ${agents[currentAgentIndex].color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                {(() => {
                  const IconComponent = agents[currentAgentIndex].icon;
                  return <IconComponent className="w-10 h-10 text-white" />;
                })()}
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{agents[currentAgentIndex].name} Co-founder</h3>
              <p className="text-purple-600 font-medium mb-4">{agents[currentAgentIndex].role}</p>
              <p className="text-gray-600 mb-6">{agents[currentAgentIndex].description}</p>
              
              <div className="flex flex-wrap justify-center gap-2 mb-6">
                {agents[currentAgentIndex].expertise.map((skill, index) => (
                  <span key={index} className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">
                    {skill}
                  </span>
                ))}
              </div>
              
              <button 
                onClick={() => setSelectedAgent(agents[currentAgentIndex].id)}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg font-medium transition-all flex items-center gap-2 mx-auto"
              >
                Customize Agent
                <ArrowRight size={16} />
              </button>
            </div>
          </motion.div>

          {/* Agent Grid */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {agents.map((agent, index) => {
              const IconComponent = agent.icon;
              return (
                <motion.button
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  onClick={() => setCurrentAgentIndex(index)}
                  className={`p-4 rounded-xl border-2 transition-all hover:scale-105 ${
                    currentAgentIndex === index
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div className={`w-12 h-12 bg-gradient-to-br ${agent.color} rounded-lg flex items-center justify-center mx-auto mb-3`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="font-medium text-gray-900 text-sm">{agent.name}</h4>
                </motion.button>
              );
            })}
          </div>
        </div>
      </section>

      {/* LiveKit Banner */}
      <section className="py-16 bg-gradient-to-r from-green-500 to-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <Video className="w-8 h-8 text-white" />
              <h2 className="text-3xl font-bold text-white">Voice & Video Coaching</h2>
            </div>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Get real-time guidance through live video sessions. See your co-founders in action, 
              ask questions, and collaborate face-to-face.
            </p>
            <button 
              onClick={() => {
                if (isAuthenticated) {
                  setShowLiveKitModal(true);
                } else {
                  onShowAuth();
                }
              }}
              className="px-8 py-4 bg-white text-green-600 rounded-xl font-semibold text-lg transition-all hover:scale-105 shadow-lg flex items-center gap-3 mx-auto"
            >
              <Play size={20} />
              Start Live Session
            </button>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">Ultimate Co-founder</span>
              </div>
              <p className="text-gray-400">
                Your AI co-founder team for building successful startups.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/documentation" className="hover:text-white transition-colors">Documentation</Link></li>
                <li><Link to="/data-room" className="hover:text-white transition-colors">Data Room</Link></li>
                <li><Link to="/api" className="hover:text-white transition-colors">API</Link></li>
                <li><Link to="/integrations" className="hover:text-white transition-colors">Integrations</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link to="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link to="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link to="/careers" className="hover:text-white transition-colors">Careers</Link></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <div className="flex gap-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Twitter size={20} />
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Linkedin size={20} />
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <Github size={20} />
                </a>
              </div>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Ultimate Co-founder. All rights reserved.
            </p>
            <div className="flex gap-6 text-sm text-gray-400 mt-4 md:mt-0">
              <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>

      {/* LiveKit Modal */}
      <AnimatePresence>
        {showLiveKitModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowLiveKitModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-8 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Video className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Start Live Session</h3>
                <p className="text-gray-600 mb-6">
                  Connect with your AI co-founder team through video and voice. 
                  Get real-time guidance and collaborative problem-solving.
                </p>
                
                <div className="space-y-3">
                  <button 
                    onClick={() => handleStartLiveKit('video')}
                    className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-all flex items-center justify-center gap-2"
                  >
                    <Video size={16} />
                    Video Call
                  </button>
                  <button 
                    onClick={() => handleStartLiveKit('voice')}
                    className="w-full px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-900 rounded-lg font-medium transition-all flex items-center justify-center gap-2"
                  >
                    <Phone size={16} />
                    Voice Only
                  </button>
                  <button 
                    onClick={() => handleStartLiveKit('screen-share')}
                    className="w-full px-4 py-3 bg-blue-100 hover:bg-blue-200 text-blue-900 rounded-lg font-medium transition-all flex items-center justify-center gap-2"
                  >
                    <Monitor size={16} />
                    Screen Share
                  </button>
                </div>
                
                <button
                  onClick={() => setShowLiveKitModal(false)}
                  className="mt-4 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};