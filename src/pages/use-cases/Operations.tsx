import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  Settings, 
  Users, 
  BarChart3, 
  CheckCircle, 
  ArrowRight,
  Briefcase,
  Target,
  Clock,
  Shield,
  TrendingUp,
  FileText
} from 'lucide-react';

export const Operations: React.FC = () => {
  const operationalAreas = [
    {
      icon: Users,
      title: 'Team Building & Hiring',
      description: 'Build high-performing teams with strategic hiring and onboarding processes'
    },
    {
      icon: Settings,
      title: 'Process Optimization',
      description: 'Streamline operations and workflows for maximum efficiency and scalability'
    },
    {
      icon: BarChart3,
      title: 'Performance Metrics',
      description: 'Implement KPIs and tracking systems to measure and improve performance'
    },
    {
      icon: Shield,
      title: 'Legal & Compliance',
      description: 'Navigate legal requirements and ensure compliance across all operations'
    }
  ];

  const hiringProcess = [
    {
      step: '01',
      title: 'Role Definition',
      description: 'Define clear job descriptions, requirements, and success criteria',
      icon: Target
    },
    {
      step: '02',
      title: 'Sourcing Strategy',
      description: 'Develop effective sourcing strategies to attract top talent',
      icon: Users
    },
    {
      step: '03',
      title: 'Interview Process',
      description: 'Design structured interview processes that identify the best candidates',
      icon: Briefcase
    },
    {
      step: '04',
      title: 'Onboarding & Integration',
      description: 'Create seamless onboarding experiences that set new hires up for success',
      icon: Clock
    }
  ];

  const operationalBenefits = [
    'Reduced operational costs and inefficiencies',
    'Faster hiring and onboarding processes',
    'Improved team productivity and performance',
    'Better compliance and risk management',
    'Scalable systems and processes',
    'Clear performance metrics and accountability',
    'Enhanced employee satisfaction and retention',
    'Streamlined workflows and automation'
  ];

  const keyMetrics = [
    {
      metric: 'Time to Hire',
      description: 'Reduce hiring time from weeks to days',
      icon: Clock,
      improvement: '60% faster'
    },
    {
      metric: 'Employee Retention',
      description: 'Improve retention with better processes',
      icon: Users,
      improvement: '40% increase'
    },
    {
      metric: 'Operational Efficiency',
      description: 'Streamline processes and reduce waste',
      icon: TrendingUp,
      improvement: '50% improvement'
    },
    {
      metric: 'Compliance Score',
      description: 'Maintain high compliance standards',
      icon: Shield,
      improvement: '95% compliance'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Operations & Hiring</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Operations & Hiring Excellence
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
              Partner with our AI Operations Co-founder to build efficient operations, 
              hire top talent, and create scalable systems that grow with your startup.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/dashboard" 
                className="px-8 py-4 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
              >
                Optimize Operations
                <ArrowRight size={20} />
              </Link>
              <Link 
                to="/ai-cofounders" 
                className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
              >
                Meet the Team
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Operational Areas */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Core Operational Areas
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our AI helps you excel in all critical operational areas that drive startup success and scalability.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {operationalAreas.map((area, index) => {
              const IconComponent = area.icon;
              return (
                <div key={index} className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-6 hover:shadow-lg transition-shadow">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {area.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {area.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Hiring Process */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Strategic Hiring Process
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Follow our proven 4-step hiring process to build high-performing teams that drive your startup forward.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {hiringProcess.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                  <div className="flex items-start gap-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-orange-600 dark:text-orange-400 mb-2">
                        Step {step.step}
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        {step.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Key Metrics */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Measurable Operational Improvements
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Track and improve key operational metrics that directly impact your startup's success and growth.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {keyMetrics.map((metric, index) => {
              const IconComponent = metric.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                    {metric.metric}
                  </h3>
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-3">
                    {metric.improvement}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {metric.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Operational Excellence Benefits
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Working with our AI Operations Co-founder delivers tangible improvements across all aspects of your startup operations.
              </p>
              <div className="space-y-4">
                {operationalBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-2xl p-8">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Settings className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Scale Your Operations?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start optimizing your operations and building your dream team with our AI Operations Co-founder.
                </p>
                <Link 
                  to="/dashboard" 
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white rounded-lg font-semibold transition-all hover:shadow-lg"
                >
                  Start Optimizing
                  <ArrowRight size={16} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Why Choose AI-Powered Operations
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Our AI Operations Co-founder combines best practices with real-time insights to help you build world-class operations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Data-Driven Decisions
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Make operational decisions based on real data and proven best practices from successful startups.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Process Templates
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Access proven templates and frameworks for hiring, onboarding, and operational processes.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Scalable Systems
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Build operations that scale with your growth, from startup to enterprise.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
