import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  Target, 
  Users, 
  TrendingUp, 
  CheckCircle, 
  ArrowRight,
  Lightbulb,
  BarChart3,
  Search,
  Layers,
  Clock,
  Star
} from 'lucide-react';

export const ProductStrategy: React.FC = () => {
  const benefits = [
    {
      icon: Target,
      title: 'Product-Market Fit',
      description: 'Validate your product concept and ensure it meets real market needs'
    },
    {
      icon: Users,
      title: 'User-Centric Design',
      description: 'Build products that users love with data-driven design decisions'
    },
    {
      icon: TrendingUp,
      title: 'Growth Strategy',
      description: 'Plan sustainable growth with clear metrics and milestones'
    },
    {
      icon: BarChart3,
      title: 'Data-Driven Decisions',
      description: 'Make informed product decisions based on user feedback and analytics'
    }
  ];

  const process = [
    {
      step: '01',
      title: 'Market Research',
      description: 'Analyze your target market, competitors, and identify opportunities',
      icon: Search
    },
    {
      step: '02',
      title: 'Product Definition',
      description: 'Define your MVP features, user personas, and value proposition',
      icon: Target
    },
    {
      step: '03',
      title: 'Roadmap Planning',
      description: 'Create a strategic roadmap with prioritized features and timelines',
      icon: Layers
    },
    {
      step: '04',
      title: 'Execution & Iteration',
      description: 'Launch, measure, learn, and iterate based on user feedback',
      icon: Clock
    }
  ];

  const useCases = [
    'Validate your startup idea with market research',
    'Define your minimum viable product (MVP)',
    'Create user personas and customer journey maps',
    'Prioritize features for maximum impact',
    'Plan product iterations and updates',
    'Analyze competitor strategies',
    'Set up product metrics and KPIs',
    'Design user onboarding flows'
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Product Strategy</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                Product Strategy That Drives Success
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
                Work with our AI Product Co-founder to develop winning product strategies, 
                validate your ideas, and build products that users love.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  to="/dashboard" 
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
                >
                  Start Planning
                  <ArrowRight size={20} />
                </Link>
                <Link 
                  to="/ai-cofounders" 
                  className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
                >
                  Meet the Team
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-8">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <Target className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">Market Fit</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Validate product-market fit</p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <Users className="w-8 h-8 text-cyan-600 mb-2" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">User Research</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Understand your users</p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <Layers className="w-8 h-8 text-blue-600 mb-2" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">Roadmapping</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Strategic planning</p>
                  </div>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <BarChart3 className="w-8 h-8 text-cyan-600 mb-2" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">Analytics</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Data-driven insights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Why Product Strategy Matters
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              A solid product strategy is the foundation of every successful startup. 
              Our AI helps you build products that solve real problems and delight users.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {benefit.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Our Product Strategy Process
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Follow our proven 4-step process to develop a winning product strategy that drives growth and success.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <div key={index} className="relative">
                  <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-2xl font-bold text-blue-600 mb-2">{step.step}</div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {step.description}
                      </p>
                    </div>
                  </div>
                  {index < process.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                      <ArrowRight className="w-6 h-6 text-gray-400" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                What You Can Accomplish
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
                Our AI Product Co-founder helps you tackle the most critical product strategy challenges 
                that every startup faces.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {useCases.map((useCase, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">{useCase}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-8">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Star className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Build Something Amazing?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start working with our AI Product Co-founder today and turn your ideas into successful products.
                </p>
                <Link 
                  to="/dashboard" 
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-lg font-semibold transition-all hover:shadow-lg"
                >
                  Start Free Trial
                  <ArrowRight size={16} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
