import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Code, 
  Copy, 
  Check, 
  ExternalLink, 
  ChevronDown, 
  ChevronRight,
  Zap,
  ArrowLeft,
  Key,
  Lock,
  Globe,
  Server,
  Database,
  Video,
  FileText,
  Search,
  Users
} from 'lucide-react';

export const API: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedSection, setExpandedSection] = useState<string | null>('authentication');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<'curl' | 'javascript' | 'python'>('javascript');

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  const codeExamples = {
    authentication: {
      curl: `curl -X POST https://api.ultimate-cofounder.com/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "your-password"}'`,
      javascript: `import axios from 'axios';

const login = async (email, password) => {
  try {
    const response = await axios.post('https://api.ultimate-cofounder.com/v1/auth/login', {
      email,
      password
    });
    
    const { access_token } = response.data;
    
    // Store the token for future requests
    localStorage.setItem('access_token', access_token);
    
    return response.data;
  } catch (error) {
    console.error('Authentication failed:', error);
    throw error;
  }
};`,
      python: `import requests

def login(email, password):
    try:
        response = requests.post(
            'https://api.ultimate-cofounder.com/v1/auth/login',
            json={'email': email, 'password': password}
        )
        response.raise_for_status()
        
        data = response.json()
        access_token = data['access_token']
        
        # Store the token for future requests
        return data
    except Exception as e:
        print(f"Authentication failed: {e}")
        raise`
    },
    agents: {
      curl: `curl -X POST https://api.ultimate-cofounder.com/v1/agents/execute \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "agent_id": "strategic",
    "description": "Analyze the market for a SaaS project management tool",
    "context": "Targeting small to medium businesses"
  }'`,
      javascript: `import axios from 'axios';

const executeAgentTask = async (agentId, description, context = null) => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.ultimate-cofounder.com/v1/agents/execute',
      {
        agent_id: agentId,
        description,
        context
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Task execution failed:', error);
    throw error;
  }
};`,
      python: `import requests

def execute_agent_task(agent_id, description, context=None, access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'agent_id': agent_id,
            'description': description
        }
        
        if context:
            payload['context'] = context
            
        response = requests.post(
            'https://api.ultimate-cofounder.com/v1/agents/execute',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        print(f"Task execution failed: {e}")
        raise`
    },
    livekit: {
      curl: `curl -X POST https://api.ultimate-cofounder.com/v1/livekit/sessions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "agent_ids": ["strategic", "product"],
    "session_type": "video"
  }'`,
      javascript: `import axios from 'axios';

const createLiveKitSession = async (agentIds, sessionType = 'video') => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.ultimate-cofounder.com/v1/livekit/sessions',
      {
        agent_ids: agentIds,
        session_type: sessionType
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Failed to create LiveKit session:', error);
    throw error;
  }
};`,
      python: `import requests

def create_livekit_session(agent_ids, session_type='video', access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'agent_ids': agent_ids,
            'session_type': session_type
        }
            
        response = requests.post(
            'https://api.ultimate-cofounder.com/v1/livekit/sessions',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    except Exception as e:
        print(f"Failed to create LiveKit session: {e}")
        raise`
    },
    bolt: {
      curl: `curl -X POST https://api.ultimate-cofounder.com/v1/bolt/scaffold \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -d '{
    "prompt": "Restaurant management app with reservation system",
    "tech_stack": "React + Node.js + PostgreSQL"
  }'`,
      javascript: `import axios from 'axios';

const generateScaffold = async (prompt, techStack) => {
  try {
    const token = localStorage.getItem('access_token');
    
    const response = await axios.post(
      'https://api.ultimate-cofounder.com/v1/bolt/scaffold',
      {
        prompt,
        tech_stack: techStack
      },
      {
        headers: {
          'Authorization': \`Bearer \${token}\`
        }
      }
    );
    
    return response.data.files;
  } catch (error) {
    console.error('Failed to generate scaffold:', error);
    throw error;
  }
};`,
      python: `import requests

def generate_scaffold(prompt, tech_stack, access_token=None):
    try:
        headers = {
            'Authorization': f'Bearer {access_token}'
        }
        
        payload = {
            'prompt': prompt,
            'tech_stack': tech_stack
        }
            
        response = requests.post(
            'https://api.ultimate-cofounder.com/v1/bolt/scaffold',
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()['files']
    except Exception as e:
        print(f"Failed to generate scaffold: {e}")
        raise`
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">API Reference</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search API docs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sticky top-24">
              <h2 className="font-bold text-gray-900 dark:text-white mb-4">API Reference</h2>
              
              <div className="space-y-1 mb-6">
                <button
                  onClick={() => toggleSection('authentication')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    expandedSection === 'authentication'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Key className="w-4 h-4" />
                    <span>Authentication</span>
                  </div>
                  {expandedSection === 'authentication' ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => toggleSection('agents')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    expandedSection === 'agents'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    <span>Agents</span>
                  </div>
                  {expandedSection === 'agents' ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => toggleSection('livekit')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    expandedSection === 'livekit'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Video className="w-4 h-4" />
                    <span>LiveKit Sessions</span>
                  </div>
                  {expandedSection === 'livekit' ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => toggleSection('bolt')}
                  className={`w-full flex items-center justify-between p-2 rounded-lg text-left ${
                    expandedSection === 'bolt'
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Code className="w-4 h-4" />
                    <span>Bolt.new Integration</span>
                  </div>
                  {expandedSection === 'bolt' ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                </button>
              </div>

              <h3 className="font-medium text-gray-900 dark:text-white mb-2">Resources</h3>
              <ul className="space-y-1 text-sm">
                <li>
                  <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                    <FileText className="w-4 h-4" />
                    <span>API Status</span>
                  </a>
                </li>
                <li>
                  <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                    <ExternalLink className="w-4 h-4" />
                    <span>OpenAPI Spec</span>
                  </a>
                </li>
                <li>
                  <a href="#" className="text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1">
                    <Server className="w-4 h-4" />
                    <span>Rate Limits</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Ultimate Co-founder API</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Our REST API gives you programmatic access to the entire Ultimate Co-founder platform. Build custom integrations, automate workflows, and extend the capabilities of your AI co-founder team.
                </p>

                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                  <h3 className="font-medium text-purple-900 dark:text-purple-200 mb-2">Base URL</h3>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg flex items-center justify-between">
                    <code className="text-purple-700 dark:text-purple-300 font-mono">https://api.ultimate-cofounder.com/v1</code>
                    <button 
                      onClick={() => copyToClipboard('https://api.ultimate-cofounder.com/v1')}
                      className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                    >
                      {copiedCode === 'https://api.ultimate-cofounder.com/v1' ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Authentication Section */}
              {expandedSection === 'authentication' && (
                <div className="mb-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Key className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Authentication</h2>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    The Ultimate Co-founder API uses bearer token authentication. You'll need to obtain an access token by authenticating with your email and password.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Login Endpoint</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/auth/login</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      This endpoint returns an access token that you'll use for all subsequent API requests.
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden mb-4">
                      <div className="flex border-b border-gray-200 dark:border-gray-600">
                        {(['curl', 'javascript', 'python'] as const).map((lang) => (
                          <button
                            key={lang}
                            onClick={() => setSelectedLanguage(lang)}
                            className={`px-4 py-2 text-sm font-medium ${
                              selectedLanguage === lang
                                ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                            }`}
                          >
                            {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                          </button>
                        ))}
                      </div>
                      <div className="p-4 relative">
                        <button 
                          onClick={() => copyToClipboard(codeExamples.authentication[selectedLanguage])}
                          className="absolute top-4 right-4 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === codeExamples.authentication[selectedLanguage] ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                          {codeExamples.authentication[selectedLanguage]}
                        </pre>
                      </div>
                    </div>

                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Response</h4>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg relative">
                      <button 
                        onClick={() => copyToClipboard(`{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}`)}
                        className="absolute top-4 right-4 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      >
                        {copiedCode === `{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}` ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </button>
                      <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
{`{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}`}
                      </pre>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2 flex items-center gap-2">
                      <Lock className="w-4 h-4" />
                      Using Your Token
                    </h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
                      Include the token in the Authorization header for all API requests:
                    </p>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                      <code className="text-blue-700 dark:text-blue-300 font-mono">
                        Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                      </code>
                    </div>
                  </div>
                </div>
              )}

              {/* Agents Section */}
              {expandedSection === 'agents' && (
                <div className="mb-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Agents</h2>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    The Agents API allows you to interact with the AI co-founder agents, execute tasks, and retrieve results.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">List All Agents</h3>
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">GET</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/agents</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Returns a list of all available AI co-founder agents.
                    </p>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Execute Agent Task</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/agents/execute</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Execute a task with a specific agent and get the results.
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden mb-4">
                      <div className="flex border-b border-gray-200 dark:border-gray-600">
                        {(['curl', 'javascript', 'python'] as const).map((lang) => (
                          <button
                            key={lang}
                            onClick={() => setSelectedLanguage(lang)}
                            className={`px-4 py-2 text-sm font-medium ${
                              selectedLanguage === lang
                                ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                            }`}
                          >
                            {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                          </button>
                        ))}
                      </div>
                      <div className="p-4 relative">
                        <button 
                          onClick={() => copyToClipboard(codeExamples.agents[selectedLanguage])}
                          className="absolute top-4 right-4 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === codeExamples.agents[selectedLanguage] ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                          {codeExamples.agents[selectedLanguage]}
                        </pre>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Execute Multi-Agent Task</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/agents/execute-multi</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Execute a task with multiple agents collaboratively.
                    </p>
                  </div>
                </div>
              )}

              {/* LiveKit Section */}
              {expandedSection === 'livekit' && (
                <div className="mb-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Video className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">LiveKit Sessions</h2>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    The LiveKit Sessions API allows you to create and manage real-time video/voice sessions with AI co-founder agents.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Create Session</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/livekit/sessions</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Create a new LiveKit session with specified agents.
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden mb-4">
                      <div className="flex border-b border-gray-200 dark:border-gray-600">
                        {(['curl', 'javascript', 'python'] as const).map((lang) => (
                          <button
                            key={lang}
                            onClick={() => setSelectedLanguage(lang)}
                            className={`px-4 py-2 text-sm font-medium ${
                              selectedLanguage === lang
                                ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                            }`}
                          >
                            {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                          </button>
                        ))}
                      </div>
                      <div className="p-4 relative">
                        <button 
                          onClick={() => copyToClipboard(codeExamples.livekit[selectedLanguage])}
                          className="absolute top-4 right-4 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === codeExamples.livekit[selectedLanguage] ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                          {codeExamples.livekit[selectedLanguage]}
                        </pre>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Get Session</h3>
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">GET</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/livekit/sessions/{'{session_id}'}</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Get details about a specific LiveKit session.
                    </p>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">End Session</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/livekit/sessions/{'{session_id}'}/end</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      End a LiveKit session and generate recording if available.
                    </p>
                  </div>
                </div>
              )}

              {/* Bolt.new Section */}
              {expandedSection === 'bolt' && (
                <div className="mb-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Code className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white">Bolt.new Integration</h2>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    The Bolt.new Integration API allows you to generate code scaffolds and deploy projects.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Generate Scaffold</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/bolt/scaffold</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Generate code scaffold from a prompt and tech stack.
                    </p>

                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden mb-4">
                      <div className="flex border-b border-gray-200 dark:border-gray-600">
                        {(['curl', 'javascript', 'python'] as const).map((lang) => (
                          <button
                            key={lang}
                            onClick={() => setSelectedLanguage(lang)}
                            className={`px-4 py-2 text-sm font-medium ${
                              selectedLanguage === lang
                                ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400'
                                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                            }`}
                          >
                            {lang === 'curl' ? 'cURL' : lang === 'javascript' ? 'JavaScript' : 'Python'}
                          </button>
                        ))}
                      </div>
                      <div className="p-4 relative">
                        <button 
                          onClick={() => copyToClipboard(codeExamples.bolt[selectedLanguage])}
                          className="absolute top-4 right-4 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                        >
                          {copiedCode === codeExamples.bolt[selectedLanguage] ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                        </button>
                        <pre className="text-sm overflow-x-auto text-gray-800 dark:text-gray-200">
                          {codeExamples.bolt[selectedLanguage]}
                        </pre>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Create Project</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/bolt/projects</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Create a new Bolt.new project with the generated files.
                    </p>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-2 mb-3">
                      <h3 className="font-medium text-gray-900 dark:text-white">Deploy Project</h3>
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">POST</span>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mb-3">
                      <code className="text-gray-800 dark:text-gray-200 font-mono">/bolt/projects/{'{project_id}'}/deploy</code>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mb-3">
                      Deploy a Bolt.new project to a live URL.
                    </p>
                  </div>
                </div>
              )}

              {/* API Keys Section */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mt-8">
                <div className="flex items-center gap-3 mb-4">
                  <Key className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">Get Your API Key</h2>
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  To use the Ultimate Co-founder API, you'll need to generate an API key from your dashboard.
                </p>
                <Link 
                  to="/dashboard"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  <Key className="w-4 h-4" />
                  <span>Generate API Key</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};