import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { FolderOpen, FileText, Download, Upload, Search, Filter, Plus, MoreHorizontal, Trash2, Edit, Share2, Eye, CheckCircle, AlertCircle, Zap, ArrowLeft, File, FileImage, FileSpreadsheet, File as FilePdf, FileCode, Lock, Users } from 'lucide-react';

interface DataRoomFile {
  id: string;
  name: string;
  type: 'pdf' | 'xlsx' | 'docx' | 'pptx' | 'jpg' | 'png' | 'txt' | 'md';
  size: number;
  folder: string;
  lastModified: string;
  status: 'present' | 'missing' | 'outdated';
  sharedWith: string[];
}

interface DataRoomFolder {
  id: string;
  name: string;
  description: string;
  fileCount: number;
  lastModified: string;
}

export const DataRoom: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<DataRoomFile | null>(null);
  const [showFileDetails, setShowFileDetails] = useState(false);

  // Mock data
  const folders: DataRoomFolder[] = [
    {
      id: 'executive-summary',
      name: 'Executive Summary',
      description: 'High-level overview of the business',
      fileCount: 3,
      lastModified: '2025-06-28'
    },
    {
      id: 'financial-projections',
      name: 'Financial Projections',
      description: 'Financial models and forecasts',
      fileCount: 5,
      lastModified: '2025-06-27'
    },
    {
      id: 'legal-documents',
      name: 'Legal Documents',
      description: 'Contracts, agreements, and legal paperwork',
      fileCount: 7,
      lastModified: '2025-06-25'
    },
    {
      id: 'technical-documentation',
      name: 'Technical Documentation',
      description: 'Architecture, code, and technical specs',
      fileCount: 8,
      lastModified: '2025-06-29'
    },
    {
      id: 'market-research',
      name: 'Market Research',
      description: 'Market analysis and competitive research',
      fileCount: 4,
      lastModified: '2025-06-26'
    },
    {
      id: 'team-information',
      name: 'Team Information',
      description: 'Team structure, bios, and roles',
      fileCount: 2,
      lastModified: '2025-06-24'
    }
  ];

  const files: DataRoomFile[] = [
    {
      id: 'exec-summary',
      name: 'Executive Summary.pdf',
      type: 'pdf',
      size: 2500000,
      folder: 'executive-summary',
      lastModified: '2025-06-28T14:30:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'pitch-deck',
      name: 'Investor Pitch Deck.pptx',
      type: 'pptx',
      size: 8500000,
      folder: 'executive-summary',
      lastModified: '2025-06-28T10:15:00Z',
      status: 'present',
      sharedWith: ['Investors']
    },
    {
      id: 'one-pager',
      name: 'One-Page Overview.pdf',
      type: 'pdf',
      size: 1200000,
      folder: 'executive-summary',
      lastModified: '2025-06-27T16:45:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team', 'Partners']
    },
    {
      id: 'financial-model',
      name: 'Financial Model.xlsx',
      type: 'xlsx',
      size: 4500000,
      folder: 'financial-projections',
      lastModified: '2025-06-27T09:20:00Z',
      status: 'present',
      sharedWith: ['Investors']
    },
    {
      id: 'cash-flow',
      name: 'Cash Flow Projections.xlsx',
      type: 'xlsx',
      size: 3200000,
      folder: 'financial-projections',
      lastModified: '2025-06-26T11:10:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'cap-table',
      name: 'Cap Table.xlsx',
      type: 'xlsx',
      size: 1800000,
      folder: 'financial-projections',
      lastModified: '2025-06-25T15:30:00Z',
      status: 'present',
      sharedWith: ['Investors']
    },
    {
      id: 'unit-economics',
      name: 'Unit Economics.xlsx',
      type: 'xlsx',
      size: 2100000,
      folder: 'financial-projections',
      lastModified: '2025-06-24T13:45:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'budget',
      name: 'Operating Budget.xlsx',
      type: 'xlsx',
      size: 2800000,
      folder: 'financial-projections',
      lastModified: '2025-06-23T10:20:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'incorporation',
      name: 'Certificate of Incorporation.pdf',
      type: 'pdf',
      size: 3500000,
      folder: 'legal-documents',
      lastModified: '2025-06-25T09:15:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'bylaws',
      name: 'Company Bylaws.pdf',
      type: 'pdf',
      size: 4200000,
      folder: 'legal-documents',
      lastModified: '2025-06-24T14:30:00Z',
      status: 'present',
      sharedWith: ['Investors', 'Team']
    },
    {
      id: 'ip-assignment',
      name: 'IP Assignment Agreements.pdf',
      type: 'pdf',
      size: 3800000,
      folder: 'legal-documents',
      lastModified: '2025-06-23T11:45:00Z',
      status: 'present',
      sharedWith: ['Investors']
    },
    {
      id: 'testimonials',
      name: 'Customer Testimonials.pdf',
      type: 'pdf',
      size: 0,
      folder: 'market-research',
      lastModified: '2025-06-26T00:00:00Z',
      status: 'missing',
      sharedWith: []
    }
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FilePdf className="w-6 h-6 text-red-500" />;
      case 'xlsx':
        return <FileSpreadsheet className="w-6 h-6 text-green-500" />;
      case 'docx':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'pptx':
        return <FileText className="w-6 h-6 text-orange-500" />;
      case 'jpg':
      case 'png':
        return <FileImage className="w-6 h-6 text-purple-500" />;
      case 'txt':
      case 'md':
        return <File className="w-6 h-6 text-gray-500" />;
      default:
        return <File className="w-6 h-6 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredFiles = selectedFolder
    ? files.filter(file => file.folder === selectedFolder)
    : files;

  const searchedFiles = searchQuery
    ? filteredFiles.filter(file => file.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : filteredFiles;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">Ultimate Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">Data Room</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 w-64 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white"
                />
              </div>
              <Link to="/" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                <ArrowLeft className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Data Room Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Startup Data Room</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Comprehensive repository of all critical business documents
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                <Upload className="w-4 h-4" />
                <span>Upload</span>
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                <Plus className="w-4 h-4" />
                <span>New Folder</span>
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                <FolderOpen className="w-6 h-6 text-purple-600 dark:text-purple-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{folders.length}</p>
                <p className="text-sm text-purple-600 dark:text-purple-400">Folders</p>
              </div>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600 dark:text-blue-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{files.length}</p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Files</p>
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-300" />
              </div>
              <div>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {Math.round((files.filter(f => f.status === 'present').length / files.length) * 100)}%
                </p>
                <p className="text-sm text-green-600 dark:text-green-400">Completion</p>
              </div>
            </div>
          </div>

          {/* Folder Navigation */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-gray-900 dark:text-white">Folders</h3>
              <button className="text-sm text-purple-600 dark:text-purple-400 hover:underline">View All</button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {folders.map((folder) => (
                <button
                  key={folder.id}
                  onClick={() => setSelectedFolder(folder.id)}
                  className={`p-4 rounded-lg text-left transition-colors ${
                    selectedFolder === folder.id
                      ? 'bg-purple-100 dark:bg-purple-900/30 border-2 border-purple-500'
                      : 'bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 border-2 border-transparent'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <FolderOpen className={`w-6 h-6 ${
                      selectedFolder === folder.id
                        ? 'text-purple-600 dark:text-purple-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`} />
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{folder.name}</h4>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{folder.description}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>{folder.fileCount} files</span>
                        <span>Updated {new Date(folder.lastModified).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Files Table */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-gray-900 dark:text-white">
                {selectedFolder 
                  ? `Files in ${folders.find(f => f.id === selectedFolder)?.name}` 
                  : 'All Files'}
              </h3>
              <div className="flex items-center gap-3">
                {selectedFolder && (
                  <button 
                    onClick={() => setSelectedFolder(null)}
                    className="text-sm text-purple-600 dark:text-purple-400 hover:underline flex items-center gap-1"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Back to All
                  </button>
                )}
                <button className="p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                  <Filter className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                </button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Folder</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Size</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Last Modified</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {searchedFiles.map((file) => (
                    <tr 
                      key={file.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      onClick={() => {
                        setSelectedFile(file);
                        setShowFileDetails(true);
                      }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-3">
                          {getFileIcon(file.type)}
                          <span className="text-gray-900 dark:text-white">{file.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-600 dark:text-gray-400">
                          {folders.find(f => f.id === file.folder)?.name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-600 dark:text-gray-400">
                          {file.status === 'missing' ? '-' : formatFileSize(file.size)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-600 dark:text-gray-400">
                          {file.status === 'missing' ? '-' : new Date(file.lastModified).toLocaleDateString()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {file.status === 'present' ? (
                          <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">
                            Present
                          </span>
                        ) : file.status === 'missing' ? (
                          <span className="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full text-xs">
                            Missing
                          </span>
                        ) : (
                          <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-xs">
                            Outdated
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {file.status === 'present' && (
                            <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                              <Download className="w-4 h-4" />
                            </button>
                          )}
                          {file.status === 'missing' && (
                            <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                              <Upload className="w-4 h-4" />
                            </button>
                          )}
                          <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                            <MoreHorizontal className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* File Details Modal */}
      {showFileDetails && selectedFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  {getFileIcon(selectedFile.type)}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">{selectedFile.name}</h3>
                </div>
                <button 
                  onClick={() => setShowFileDetails(false)}
                  className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Folder</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {folders.find(f => f.id === selectedFile.folder)?.name}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Size</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedFile.status === 'missing' ? '-' : formatFileSize(selectedFile.size)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Last Modified</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedFile.status === 'missing' ? '-' : new Date(selectedFile.lastModified).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                    <p className="font-medium text-gray-900 dark:text-white flex items-center gap-2">
                      {selectedFile.status === 'present' ? (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Present</span>
                        </>
                      ) : selectedFile.status === 'missing' ? (
                        <>
                          <AlertCircle className="w-4 h-4 text-red-500" />
                          <span>Missing</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-4 h-4 text-yellow-500" />
                          <span>Outdated</span>
                        </>
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Type</p>
                    <p className="font-medium text-gray-900 dark:text-white uppercase">
                      {selectedFile.type}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Shared With</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {selectedFile.sharedWith.length > 0 ? (
                        selectedFile.sharedWith.map((group, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs"
                          >
                            {group}
                          </span>
                        ))
                      ) : (
                        <span className="text-gray-500 dark:text-gray-400 text-sm">Not shared</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  {selectedFile.status === 'present' ? (
                    <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </button>
                  ) : (
                    <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                      <Upload className="w-4 h-4" />
                      <span>Upload</span>
                    </button>
                  )}
                  <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
                <div className="flex items-center gap-2">
                  <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-red-500 hover:text-red-700">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};