import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  Users, 
  MessageSquare, 
  Calendar, 
  ArrowRight,
  Heart,
  Share2,
  Trophy,
  Star,
  Globe,
  Slack,
  Github,
  Twitter,
  Linkedin,
  BookOpen,
  Lightbulb,
  Target,
  TrendingUp
} from 'lucide-react';

export const Community: React.FC = () => {
  const communityStats = [
    { label: 'Active Members', value: '12,500+', icon: Users },
    { label: 'Success Stories', value: '850+', icon: Trophy },
    { label: 'Monthly Events', value: '25+', icon: Calendar },
    { label: 'Countries', value: '75+', icon: Globe }
  ];

  const communityChannels = [
    {
      name: 'Discord Community',
      description: 'Join our vibrant Discord server for real-time discussions, Q&A sessions, and networking.',
      members: '8,500+',
      icon: MessageSquare,
      color: 'from-indigo-500 to-purple-600',
      link: '#'
    },
    {
      name: 'Slack Workspace',
      description: 'Professional discussions, industry insights, and collaboration opportunities.',
      members: '3,200+',
      icon: Slack,
      color: 'from-green-500 to-emerald-600',
      link: '#'
    },
    {
      name: 'GitHub Community',
      description: 'Open source projects, code reviews, and technical discussions.',
      members: '1,800+',
      icon: Github,
      color: 'from-gray-700 to-gray-900',
      link: '#'
    },
    {
      name: 'LinkedIn Group',
      description: 'Professional networking, industry news, and career opportunities.',
      members: '5,400+',
      icon: Linkedin,
      color: 'from-blue-600 to-blue-800',
      link: '#'
    }
  ];

  const upcomingEvents = [
    {
      id: 1,
      title: 'AI Co-founder Demo Day',
      date: '2024-02-20',
      time: '3:00 PM EST',
      type: 'Virtual Event',
      attendees: 450,
      description: 'Watch startups showcase their AI-powered solutions and get feedback from experts.'
    },
    {
      id: 2,
      title: 'Startup Pitch Practice Session',
      date: '2024-02-25',
      time: '2:00 PM EST',
      type: 'Interactive Workshop',
      attendees: 120,
      description: 'Practice your pitch with fellow entrepreneurs and get constructive feedback.'
    },
    {
      id: 3,
      title: 'Monthly Founder Meetup',
      date: '2024-03-01',
      time: '6:00 PM EST',
      type: 'Networking Event',
      attendees: 200,
      description: 'Connect with other founders, share experiences, and build lasting relationships.'
    }
  ];

  const successStories = [
    {
      id: 1,
      founder: 'Sarah Chen',
      company: 'TechFlow AI',
      story: 'Used AI co-founders to validate product-market fit and raised $2M seed round in 6 months.',
      achievement: '$2M Raised',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80'
    },
    {
      id: 2,
      founder: 'Marcus Rodriguez',
      company: 'DataSync Pro',
      story: 'Leveraged AI insights to pivot business model and achieve 300% growth in user base.',
      achievement: '300% Growth',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80'
    },
    {
      id: 3,
      founder: 'Emily Watson',
      company: 'GreenTech Solutions',
      story: 'Built MVP in 3 months with AI guidance and secured first enterprise client.',
      achievement: 'First Enterprise Client',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=256&q=80'
    }
  ];

  const communityFeatures = [
    {
      icon: MessageSquare,
      title: 'Expert Q&A Sessions',
      description: 'Get answers from industry experts and successful entrepreneurs'
    },
    {
      icon: BookOpen,
      title: 'Resource Library',
      description: 'Access exclusive templates, guides, and startup resources'
    },
    {
      icon: Users,
      title: 'Peer Mentorship',
      description: 'Connect with mentors and fellow entrepreneurs at your stage'
    },
    {
      icon: Calendar,
      title: 'Regular Events',
      description: 'Join workshops, webinars, and networking events'
    },
    {
      icon: Trophy,
      title: 'Success Tracking',
      description: 'Celebrate milestones and learn from success stories'
    },
    {
      icon: Lightbulb,
      title: 'Idea Validation',
      description: 'Get feedback on your ideas from the community'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">Community</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Join Community
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Join the AI Co-founder Community
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
            Connect with thousands of entrepreneurs who are building the future with AI co-founders. 
            Share experiences, get support, and grow together.
          </p>
          <Link 
            to="/dashboard" 
            className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
          >
            Join Now - Free
            <ArrowRight size={20} />
          </Link>
        </div>
      </section>

      {/* Community Stats */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {communityStats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Community Channels */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Choose Your Platform
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Join us on your preferred platform and start connecting with fellow entrepreneurs today.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {communityChannels.map((channel, index) => {
              const IconComponent = channel.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                  <div className="flex items-start gap-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${channel.color} rounded-2xl flex items-center justify-center flex-shrink-0`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        {channel.name}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {channel.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-500">
                          {channel.members} members
                        </span>
                        <Link 
                          to={channel.link}
                          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
                        >
                          Join
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Upcoming Events */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Upcoming Events
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Join our regular events to learn, network, and grow your startup with the community.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-sm font-medium rounded-full">
                    {event.type}
                  </span>
                  <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                    <Users size={14} />
                    {event.attendees}
                  </div>
                </div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {event.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                  {event.description}
                </p>
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
                  <div className="flex items-center gap-1">
                    <Calendar size={14} />
                    {new Date(event.date).toLocaleDateString()}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock size={14} />
                    {event.time}
                  </div>
                </div>
                <button className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors">
                  Register
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Community Success Stories
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Be inspired by the achievements of our community members who are building successful startups with AI co-founders.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {successStories.map((story) => (
              <div key={story.id} className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg">
                <div className="flex items-center gap-4 mb-4">
                  <img 
                    src={story.avatar} 
                    alt={story.founder}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white">
                      {story.founder}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {story.company}
                    </p>
                  </div>
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  "{story.story}"
                </p>
                <div className="flex items-center justify-between">
                  <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-sm font-medium rounded-full">
                    {story.achievement}
                  </span>
                  <div className="flex items-center gap-1">
                    <Heart className="w-4 h-4 text-red-500" />
                    <Share2 className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Community Features */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              What You Get as a Member
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Join our community and unlock exclusive benefits designed to accelerate your startup journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {communityFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-2xl p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Join Our Community?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
            Connect with like-minded entrepreneurs and start building your startup with AI co-founders today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/dashboard" 
              className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
            >
              Join Community
              <ArrowRight size={20} />
            </Link>
            <Link 
              to="/webinars" 
              className="px-8 py-4 bg-white dark:bg-gray-900 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
            >
              Browse Events
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};
