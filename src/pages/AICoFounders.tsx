import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Zap, 
  Brain, 
  Target, 
  Code, 
  Settings, 
  TrendingUp,
  Users,
  MessageSquare,
  Video,
  ArrowRight,
  CheckCircle,
  Lightbulb,
  BarChart3,
  Rocket
} from 'lucide-react';

export const AICoFounders: React.FC = () => {
  const agents = [
    {
      id: 'strategic',
      name: 'Strategic Co-founder',
      icon: Brain,
      color: 'from-purple-500 to-indigo-600',
      expertise: 'Business Strategy & Vision',
      description: 'Your strategic partner for high-level business decisions, market analysis, and long-term planning.',
      capabilities: [
        'Market research and competitive analysis',
        'Business model development',
        'Strategic planning and roadmapping',
        'Investment and funding strategies',
        'Partnership and acquisition opportunities'
      ],
      useCases: [
        'Validate your business idea',
        'Develop go-to-market strategy',
        'Plan expansion into new markets',
        'Optimize business operations'
      ]
    },
    {
      id: 'product',
      name: 'Product Co-founder',
      icon: Target,
      color: 'from-blue-500 to-cyan-600',
      expertise: 'Product Development & UX',
      description: 'Expert in product strategy, user experience design, and feature prioritization.',
      capabilities: [
        'Product roadmap planning',
        'User experience design',
        'Feature prioritization',
        'Product-market fit analysis',
        'User research and feedback analysis'
      ],
      useCases: [
        'Design your MVP features',
        'Improve user experience',
        'Plan product iterations',
        'Analyze user feedback'
      ]
    },
    {
      id: 'technical',
      name: 'Technical Co-founder',
      icon: Code,
      color: 'from-green-500 to-emerald-600',
      expertise: 'Technology & Development',
      description: 'Your technical advisor for architecture decisions, development planning, and tech stack choices.',
      capabilities: [
        'Technical architecture design',
        'Technology stack recommendations',
        'Development planning and estimation',
        'Code review and best practices',
        'Scalability and performance optimization'
      ],
      useCases: [
        'Choose the right tech stack',
        'Plan development sprints',
        'Solve technical challenges',
        'Scale your infrastructure'
      ]
    },
    {
      id: 'operations',
      name: 'Operations Co-founder',
      icon: Settings,
      color: 'from-orange-500 to-red-600',
      expertise: 'Operations & Team Building',
      description: 'Specialist in operational efficiency, team management, and process optimization.',
      capabilities: [
        'Operational process design',
        'Team structure and hiring',
        'Performance metrics and KPIs',
        'Workflow optimization',
        'Legal and compliance guidance'
      ],
      useCases: [
        'Build your team structure',
        'Optimize business processes',
        'Set up performance metrics',
        'Handle legal requirements'
      ]
    },
    {
      id: 'marketing',
      name: 'Marketing Co-founder',
      icon: TrendingUp,
      color: 'from-pink-500 to-rose-600',
      expertise: 'Marketing & Growth',
      description: 'Growth expert focused on customer acquisition, brand building, and marketing strategies.',
      capabilities: [
        'Marketing strategy development',
        'Brand positioning and messaging',
        'Customer acquisition channels',
        'Content marketing and SEO',
        'Social media and community building'
      ],
      useCases: [
        'Launch marketing campaigns',
        'Build brand awareness',
        'Acquire new customers',
        'Grow your community'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <span className="font-bold text-gray-900 dark:text-white">AI Co-founder</span>
              </Link>
              <span className="mx-4 text-gray-300 dark:text-gray-600">|</span>
              <span className="text-gray-600 dark:text-gray-400">AI Co-founders</span>
            </div>
            <Link 
              to="/dashboard" 
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              Try Now
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Meet Your AI Co-founder Team
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
            Five specialized AI co-founders working together to help you build, grow, and scale your startup. 
            Each brings unique expertise and works collaboratively to provide comprehensive business guidance.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <div className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <MessageSquare className="w-5 h-5 text-purple-600" />
              <span className="text-gray-700 dark:text-gray-300">Real-time Chat</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <Video className="w-5 h-5 text-blue-600" />
              <span className="text-gray-700 dark:text-gray-300">Video Calls</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <Users className="w-5 h-5 text-green-600" />
              <span className="text-gray-700 dark:text-gray-300">Team Collaboration</span>
            </div>
          </div>
        </div>
      </section>

      {/* AI Co-founders Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {agents.map((agent, index) => {
              const IconComponent = agent.icon;
              return (
                <div key={agent.id} className="bg-gray-50 dark:bg-gray-900 rounded-2xl p-8 hover:shadow-lg transition-shadow">
                  <div className="flex items-start gap-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${agent.color} rounded-2xl flex items-center justify-center flex-shrink-0`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        {agent.name}
                      </h3>
                      <p className="text-purple-600 dark:text-purple-400 font-medium mb-4">
                        {agent.expertise}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        {agent.description}
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Core Capabilities</h4>
                          <ul className="space-y-2">
                            {agent.capabilities.slice(0, 3).map((capability, idx) => (
                              <li key={idx} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                {capability}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Use Cases</h4>
                          <ul className="space-y-2">
                            {agent.useCases.slice(0, 3).map((useCase, idx) => (
                              <li key={idx} className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <Lightbulb className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                                {useCase}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* How They Work Together */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              How They Work Together
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Your AI co-founders collaborate seamlessly, sharing insights and building on each other's expertise 
              to provide comprehensive guidance for your startup journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Collaborative Analysis
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Each co-founder brings their unique perspective to analyze your challenges and opportunities from multiple angles.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Unified Strategy
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                They work together to create cohesive strategies that align business, product, technical, and operational goals.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Rocket className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Accelerated Growth
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                By combining their expertise, they help you make faster, more informed decisions and avoid common startup pitfalls.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-800 dark:to-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Meet Your AI Co-founder Team?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
            Start chatting with your AI co-founders today and experience the power of collaborative AI guidance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/dashboard" 
              className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg flex items-center gap-2 justify-center"
            >
              Start Free Trial
              <ArrowRight size={20} />
            </Link>
            <Link 
              to="/pricing" 
              className="px-8 py-4 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600 text-gray-900 dark:text-white rounded-xl font-semibold text-lg transition-all hover:shadow-lg"
            >
              View Pricing
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};
