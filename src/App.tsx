import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './components/AuthProvider';
import { Homepage } from './components/Homepage';
import { Dashboard } from './components/Dashboard';
import { StandaloneAuthPage } from './components/StandaloneAuthPage';
import { TopViewPage } from './components/TopViewPage';

function App() {
  const [showAuth, setShowAuth] = useState(false);

  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen">
          <Routes>
            {/* Main Routes */}
            <Route
              path="/"
              element={<Homepage onShowAuth={() => setShowAuth(true)} />}
            />
            <Route path="/dashboard/*" element={<Dashboard />} />
            <Route path="/auth" element={<StandaloneAuthPage />} />
            <Route path="/topview" element={<TopViewPage />} />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>

          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;