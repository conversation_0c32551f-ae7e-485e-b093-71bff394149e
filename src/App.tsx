import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { AuthProvider, useAuth } from './components/AuthProvider';
import { AuthPage } from './components/AuthPage';
import { StandaloneAuthPage } from './components/StandaloneAuthPage';
import { LiveKitTest } from './components/LiveKitTest';
import { LiveKitDebugPage } from './components/LiveKitDebugPage';
import { Homepage } from './components/Homepage';
import { Dashboard } from './components/Dashboard';
import { SystemHealthCheck } from './components/SystemHealthCheck';
import { apiService } from './services/api';
import { Loader2 } from 'lucide-react';
import { BrowserRouter as Router, Routes, Route, Navigate, Link } from 'react-router-dom';
import { Documentation } from './pages/Documentation';
import { DataRoom } from './pages/DataRoom';
import { API } from './pages/API';
import { Integrations } from './pages/Integrations';
import { Company } from './pages/Company';
import { Pricing } from './pages/Pricing';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { Careers } from './pages/Careers';
import { Settings } from './pages/Settings';
import { DocumentsPage } from './pages/DocumentsPage';
import { AICoFounders } from './pages/AICoFounders';
import { ProductStrategy } from './pages/use-cases/ProductStrategy';
import { GoToMarket } from './pages/use-cases/GoToMarket';
import { Fundraising } from './pages/use-cases/Fundraising';
import { Operations } from './pages/use-cases/Operations';
import { Blog } from './pages/Blog';
import { Webinars } from './pages/Webinars';
import { Community } from './pages/Community';
import { PrivacyPolicy } from './pages/PrivacyPolicy';
import { TermsOfService } from './pages/TermsOfService';
import { Security } from './pages/Security';
import { Header } from './components/Header';
import { ThemeProvider } from './components/ThemeProvider';
import { MainNavigation } from './components/MainNavigation';
import { Footer } from './components/Footer';

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const [showAuthPage, setShowAuthPage] = useState(false);
  const [isCheckingHealth, setIsCheckingHealth] = useState(true);

  useEffect(() => {
    checkBackendHealth();
  }, []);

  const checkBackendHealth = async () => {
    try {
      await apiService.healthCheck();
      console.log('✅ Backend is healthy');
    } catch (error) {
      console.warn('⚠️ Backend health check failed:', error);
    } finally {
      setIsCheckingHealth(false);
    }
  };

  // Check if URL has health-check parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('health-check')) {
      window.location.href = '/health-check';
    }
  }, []);

  if (isLoading || isCheckingHealth) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-purple-600 dark:text-purple-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading AI Co-founder...</p>
        </div>
      </div>
    );
  }

  if (showAuthPage) {
    return <AuthPage onClose={() => setShowAuthPage(false)} />;
  }

  return (
    <Router>
      <div className="flex flex-col min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white relative">
        {/* Bolt Badge - Sticky on top right beneath header */}
        <a
          href="https://bolt.new/"
          target="_blank"
          rel="noopener noreferrer"
          className="fixed top-20 right-4 z-40 hover:opacity-80 transition-opacity hover:scale-105"
          title="Built with Bolt.new"
        >
          <img
            src="https://github.com/kickiniteasy/bolt-hackathon-badge/blob/main/src/public/bolt-badge/white_circle_360x360/white_circle_360x360.png?raw=true"
            alt="Built with Bolt.new"
            className="h-16 w-16 rounded-full shadow-lg"
          />
        </a>

        <Routes>
          <Route path="/" element={<Homepage onShowAuth={() => setShowAuthPage(true)} />} />
          <Route path="*" element={<MainNavigation />} />
        </Routes>
        
        <div className="flex-grow">
          <Routes>
            <Route path="/" element={null} />
            <Route 
              path="/dashboard/*" 
              element={isAuthenticated ? <Dashboard /> : <Navigate to="/" />} 
            />
            <Route path="/auth" element={<StandaloneAuthPage />} />
            <Route path="/livekit-test" element={<LiveKitTest />} />
            <Route path="/livekit-debug" element={<LiveKitDebugPage />} />
            <Route path="/health-check" element={<SystemHealthCheck />} />
            <Route path="/documentation" element={<Documentation />} />
            <Route path="/data-room" element={<DataRoom />} />
            <Route path="/api" element={<API />} />
            <Route path="/integrations" element={<Integrations />} />
            <Route path="/company" element={<Company />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/careers" element={<Careers />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/documents" element={<DocumentsPage />} />
            <Route path="/ai-cofounders" element={<AICoFounders />} />
            <Route path="/use-cases/product-strategy" element={<ProductStrategy />} />
            <Route path="/use-cases/go-to-market" element={<GoToMarket />} />
            <Route path="/use-cases/fundraising" element={<Fundraising />} />
            <Route path="/use-cases/operations" element={<Operations />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/webinars" element={<Webinars />} />
            <Route path="/community" element={<Community />} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/terms-of-service" element={<TermsOfService />} />
            <Route path="/security" element={<Security />} />
          </Routes>
        </div>
        
        <Footer />
      </div>
    </Router>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppContent />
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;