import React from 'react';

console.log('🚀 App.tsx is loading...');

// Simple test app first
const SimpleTestApp: React.FC = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🚀 React App Test</h1>
      <p>✅ React is rendering successfully!</p>
      <p>✅ JavaScript is executing!</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}>
        <h2>Debug Information</h2>
        <p>If you can see this, the basic React setup is working.</p>
        <p>The issue was likely in one of the imported components.</p>
      </div>
    </div>
  );
};

console.log('✅ SimpleTestApp component defined');

function App() {
  console.log('🚀 App function is executing');

  return <SimpleTestApp />;
}

console.log('✅ App function defined');

export default App;