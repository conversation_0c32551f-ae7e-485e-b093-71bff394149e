import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './components/AuthProvider';
import { Homepage } from './components/Homepage';
import { Dashboard } from './components/Dashboard';
import { StandaloneAuthPage } from './components/StandaloneAuthPage';
import { TopViewPage } from './components/TopViewPage';

// Import all navigation pages
import { AICoFounders } from './pages/AICoFounders';
import { ProductStrategy } from './pages/use-cases/ProductStrategy';
import { GoToMarket } from './pages/use-cases/GoToMarket';
import { Fundraising } from './pages/use-cases/Fundraising';
import { Operations } from './pages/use-cases/Operations';
import { Blog } from './pages/Blog';
import { Webinars } from './pages/Webinars';
import { Community } from './pages/Community';
import { PrivacyPolicy } from './pages/PrivacyPolicy';
import { TermsOfService } from './pages/TermsOfService';
import { Security } from './pages/Security';
import { API } from './pages/API';
import { Documentation } from './pages/Documentation';

function App() {
  const [showAuth, setShowAuth] = useState(false);

  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen">
          <Routes>
            {/* Main Routes */}
            <Route
              path="/"
              element={<Homepage onShowAuth={() => setShowAuth(true)} />}
            />
            <Route path="/dashboard/*" element={<Dashboard />} />
            <Route path="/auth" element={<StandaloneAuthPage />} />
            <Route path="/topview" element={<TopViewPage />} />

            {/* Navigation Pages */}
            <Route path="/ai-cofounders" element={<AICoFounders />} />
            <Route path="/product-strategy" element={<ProductStrategy />} />
            <Route path="/go-to-market" element={<GoToMarket />} />
            <Route path="/fundraising" element={<Fundraising />} />
            <Route path="/operations" element={<Operations />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/webinars" element={<Webinars />} />
            <Route path="/community" element={<Community />} />
            <Route path="/api" element={<API />} />
            <Route path="/documentation" element={<Documentation />} />

            {/* Legal Pages */}
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/terms" element={<TermsOfService />} />
            <Route path="/security" element={<Security />} />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>

          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;