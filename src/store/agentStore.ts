import { create } from 'zustand';
import { Agent, Message, Project } from '../types/agent';

interface AgentStore {
  agents: Agent[];
  messages: Message[];
  activeProject: Project | null;
  projects: Project[];
  selectedAgent: Agent | null;
  isVideoCallActive: boolean;
  
  // Actions
  setSelectedAgent: (agent: Agent | null) => void;
  addMessage: (message: Message) => void;
  updateAgentStatus: (agentId: string, status: Agent['status']) => void;
  toggleVideoCall: () => void;
  setActiveProject: (project: Project | null) => void;
}

const mockAgents: Agent[] = [
  {
    id: 'strategic',
    name: 'Alex Strategic',
    role: 'strategic',
    status: 'active',
    avatar: '🎯',
    description: 'Strategic planning, market analysis, and business direction',
    capabilities: ['Market Research', 'Strategic Planning', 'Competitive Analysis', 'Business Modeling'],
    currentTask: 'Analyzing market opportunities for Q1 2025',
    metrics: {
      tasksCompleted: 47,
      collaborations: 23,
      successRate: 94,
      uptime: '99.2%'
    }
  },
  {
    id: 'product',
    name: 'Sam Product',
    role: 'product',
    status: 'thinking',
    avatar: '🚀',
    description: 'Product strategy, user experience, and feature prioritization',
    capabilities: ['Product Strategy', 'UX Design', 'Feature Planning', 'User Research'],
    currentTask: 'Designing onboarding flow improvements',
    metrics: {
      tasksCompleted: 38,
      collaborations: 31,
      successRate: 91,
      uptime: '98.7%'
    }
  },
  {
    id: 'technical',
    name: 'Taylor Tech',
    role: 'technical',
    status: 'active',
    avatar: '⚡',
    description: 'Architecture, development, and technical implementation',
    capabilities: ['System Architecture', 'Code Generation', 'Technical Review', 'DevOps'],
    currentTask: 'Optimizing API performance and scalability',
    metrics: {
      tasksCompleted: 62,
      collaborations: 28,
      successRate: 96,
      uptime: '99.8%'
    }
  },
  {
    id: 'operations',
    name: 'Jordan Ops',
    role: 'operations',
    status: 'collaborating',
    avatar: '⚙️',
    description: 'Process optimization, resource management, and operations',
    capabilities: ['Process Design', 'Resource Planning', 'Quality Assurance', 'Automation'],
    currentTask: 'Streamlining deployment pipeline',
    metrics: {
      tasksCompleted: 41,
      collaborations: 35,
      successRate: 88,
      uptime: '97.9%'
    }
  },
  {
    id: 'marketing',
    name: 'Morgan Marketing',
    role: 'marketing',
    status: 'idle',
    avatar: '📈',
    description: 'Growth strategy, brand building, and customer acquisition',
    capabilities: ['Growth Strategy', 'Content Creation', 'Analytics', 'Campaign Management'],
    currentTask: 'Planning Q1 content calendar',
    metrics: {
      tasksCompleted: 33,
      collaborations: 26,
      successRate: 92,
      uptime: '98.4%'
    }
  }
];

export const useAgentStore = create<AgentStore>((set, get) => ({
  agents: mockAgents,
  messages: [],
  activeProject: null,
  projects: [],
  selectedAgent: null,
  isVideoCallActive: false,

  setSelectedAgent: (agent) => set({ selectedAgent: agent }),
  
  addMessage: (message) => set((state) => ({
    messages: [...state.messages, message]
  })),
  
  updateAgentStatus: (agentId, status) => set((state) => ({
    agents: state.agents.map(agent => 
      agent.id === agentId ? { ...agent, status } : agent
    )
  })),
  
  toggleVideoCall: () => set((state) => ({
    isVideoCallActive: !state.isVideoCallActive
  })),
  
  setActiveProject: (project) => set({ activeProject: project })
}));