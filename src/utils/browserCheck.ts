/**
 * Browser compatibility and capability checks for LiveKit functionality
 */

export interface BrowserCapabilities {
  webrtc: boolean;
  websockets: boolean;
  mediaDevices: boolean;
  secureContext: boolean;
  browser: string;
  version: string;
  compatible: boolean;
  warnings: string[];
  errors: string[];
}

export function checkBrowserCapabilities(): BrowserCapabilities {
  const capabilities: BrowserCapabilities = {
    webrtc: false,
    websockets: false,
    mediaDevices: false,
    secureContext: false,
    browser: 'Unknown',
    version: 'Unknown',
    compatible: false,
    warnings: [],
    errors: []
  };

  // Detect browser
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) {
    capabilities.browser = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    capabilities.version = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Firefox')) {
    capabilities.browser = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    capabilities.version = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    capabilities.browser = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    capabilities.version = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Edge')) {
    capabilities.browser = 'Edge';
    const match = userAgent.match(/Edge\/(\d+)/);
    capabilities.version = match ? match[1] : 'Unknown';
  }

  // Check WebRTC support
  if (window.RTCPeerConnection || (window as any).webkitRTCPeerConnection || (window as any).mozRTCPeerConnection) {
    capabilities.webrtc = true;
  } else {
    capabilities.errors.push('WebRTC is not supported in this browser');
  }

  // Check WebSocket support
  if (window.WebSocket) {
    capabilities.websockets = true;
  } else {
    capabilities.errors.push('WebSocket is not supported in this browser');
  }

  // Check MediaDevices API
  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    capabilities.mediaDevices = true;
  } else {
    capabilities.errors.push('MediaDevices API is not supported in this browser');
  }

  // Check secure context (HTTPS or localhost)
  if (window.isSecureContext || location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
    capabilities.secureContext = true;
  } else {
    capabilities.warnings.push('Secure context (HTTPS) is required for full WebRTC functionality');
  }

  // Browser-specific warnings
  if (capabilities.browser === 'Safari') {
    const version = parseInt(capabilities.version);
    if (version < 14) {
      capabilities.warnings.push('Safari 14+ is recommended for best WebRTC support');
    }
  }

  if (capabilities.browser === 'Firefox') {
    const version = parseInt(capabilities.version);
    if (version < 80) {
      capabilities.warnings.push('Firefox 80+ is recommended for best WebRTC support');
    }
  }

  if (capabilities.browser === 'Chrome') {
    const version = parseInt(capabilities.version);
    if (version < 80) {
      capabilities.warnings.push('Chrome 80+ is recommended for best WebRTC support');
    }
  }

  // Overall compatibility
  capabilities.compatible = capabilities.webrtc && capabilities.websockets && capabilities.mediaDevices;

  return capabilities;
}

export function getCompatibilityMessage(capabilities: BrowserCapabilities): string {
  if (capabilities.compatible) {
    if (capabilities.warnings.length > 0) {
      return `Your browser supports live sessions, but there are some recommendations: ${capabilities.warnings.join(', ')}`;
    }
    return `Your browser (${capabilities.browser} ${capabilities.version}) fully supports live sessions!`;
  } else {
    return `Your browser is not compatible with live sessions. Issues: ${capabilities.errors.join(', ')}`;
  }
}

export function showBrowserCompatibilityAlert(): void {
  const capabilities = checkBrowserCapabilities();
  const message = getCompatibilityMessage(capabilities);
  
  if (!capabilities.compatible) {
    console.error('❌ Browser compatibility check failed:', capabilities);
    alert(`Browser Compatibility Issue:\n\n${message}\n\nPlease use a modern browser like Chrome, Firefox, Safari, or Edge.`);
  } else if (capabilities.warnings.length > 0) {
    console.warn('⚠️ Browser compatibility warnings:', capabilities);
    console.log(`ℹ️ ${message}`);
  } else {
    console.log('✅ Browser compatibility check passed:', capabilities);
  }
}

export async function checkMediaPermissions(): Promise<{
  camera: boolean;
  microphone: boolean;
  error?: string;
}> {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      video: true, 
      audio: true 
    });
    
    // Stop the stream immediately after checking
    stream.getTracks().forEach(track => track.stop());
    
    return {
      camera: true,
      microphone: true
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (errorMessage.includes('NotAllowedError') || errorMessage.includes('Permission denied')) {
      return {
        camera: false,
        microphone: false,
        error: 'Camera and microphone permissions are required for live sessions. Please allow access in your browser settings.'
      };
    } else if (errorMessage.includes('NotFoundError')) {
      return {
        camera: false,
        microphone: false,
        error: 'No camera or microphone found. Please connect a camera and microphone to use live sessions.'
      };
    } else {
      return {
        camera: false,
        microphone: false,
        error: `Media access error: ${errorMessage}`
      };
    }
  }
}

export function logBrowserInfo(): void {
  const capabilities = checkBrowserCapabilities();
  
  console.group('🌐 Browser Compatibility Report');
  console.log(`Browser: ${capabilities.browser} ${capabilities.version}`);
  console.log(`WebRTC Support: ${capabilities.webrtc ? '✅' : '❌'}`);
  console.log(`WebSocket Support: ${capabilities.websockets ? '✅' : '❌'}`);
  console.log(`MediaDevices API: ${capabilities.mediaDevices ? '✅' : '❌'}`);
  console.log(`Secure Context: ${capabilities.secureContext ? '✅' : '❌'}`);
  console.log(`Overall Compatible: ${capabilities.compatible ? '✅' : '❌'}`);
  
  if (capabilities.warnings.length > 0) {
    console.warn('Warnings:', capabilities.warnings);
  }
  
  if (capabilities.errors.length > 0) {
    console.error('Errors:', capabilities.errors);
  }
  
  console.groupEnd();
}
