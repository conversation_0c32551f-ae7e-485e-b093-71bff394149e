<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
</head>
<body>
    <h1>Test Page</h1>
    <p>This is a simple test page to verify the server is working.</p>
    <button onclick="testBackend()">Test Backend Connection</button>
    <div id="result"></div>

    <script>
        console.log('Test page loaded successfully');
        
        async function testBackend() {
            console.log('Testing backend connection...');
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                console.log('Backend response:', data);
                resultDiv.innerHTML = `<p style="color: green;">Backend is working! Response: ${JSON.stringify(data)}</p>`;
            } catch (error) {
                console.error('Backend test failed:', error);
                resultDiv.innerHTML = `<p style="color: red;">Backend test failed: ${error.message}</p>`;
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            console.log('Window loaded, testing backend...');
            testBackend();
        });
    </script>
</body>
</html>
