#!/usr/bin/env python3
"""
Navigation Links Test
Test all CTA buttons and navigation links to ensure they redirect properly
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any, List
import httpx
from datetime import datetime

# Test configuration
FRONTEND_URL = "http://localhost:5173"

class NavigationTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0, follow_redirects=True)
        self.test_results = []
        
    async def test_main_navigation_routes(self) -> bool:
        """Test all main navigation routes"""
        try:
            print("🧪 Testing Main Navigation Routes...")
            
            routes = [
                "/",
                "/auth",
                "/dashboard",
                "/documentation",
                "/data-room",
                "/api",
                "/integrations",
                "/company",
                "/pricing",
                "/about",
                "/privacy",
                "/terms",
                "/security",
                "/community",
                "/contact",
                "/topview"
            ]
            
            failed_routes = []
            
            for route in routes:
                try:
                    response = await self.client.get(f"{FRONTEND_URL}{route}")
                    if response.status_code == 200:
                        print(f"✅ {route} - OK")
                    else:
                        print(f"❌ {route} - {response.status_code}")
                        failed_routes.append(route)
                except Exception as e:
                    print(f"❌ {route} - Error: {e}")
                    failed_routes.append(route)
            
            if failed_routes:
                print(f"❌ Failed routes: {failed_routes}")
                return False
            
            print("✅ All main navigation routes working")
            return True
            
        except Exception as e:
            print(f"❌ Navigation routes test error: {e}")
            return False
    
    async def test_homepage_content(self) -> bool:
        """Test homepage content and CTA buttons"""
        try:
            print("🧪 Testing Homepage Content...")
            
            response = await self.client.get(FRONTEND_URL)
            if response.status_code != 200:
                print(f"❌ Homepage not accessible: {response.status_code}")
                return False
            
            content = response.text
            
            # Check for key CTA button text
            cta_texts = [
                "Try It Now - Free",
                "Start Live Session",
                "Sign In",
                "Chat with"
            ]
            
            missing_ctas = []
            for cta in cta_texts:
                if cta not in content:
                    missing_ctas.append(cta)
            
            if missing_ctas:
                print(f"❌ Missing CTA buttons: {missing_ctas}")
                return False
            
            # Check for Bolt hackathon badge
            if "bolt.new" not in content:
                print("⚠️  Bolt hackathon badge not found")
            else:
                print("✅ Bolt hackathon badge found")
            
            print("✅ Homepage content and CTA buttons present")
            return True
            
        except Exception as e:
            print(f"❌ Homepage content test error: {e}")
            return False
    
    async def test_auth_page_functionality(self) -> bool:
        """Test auth page functionality"""
        try:
            print("🧪 Testing Auth Page Functionality...")
            
            response = await self.client.get(f"{FRONTEND_URL}/auth")
            if response.status_code != 200:
                print(f"❌ Auth page not accessible: {response.status_code}")
                return False
            
            content = response.text
            
            # Check for login/register forms
            required_elements = [
                "Sign In",
                "Create Account",
                "Email",
                "Password",
                "Welcome Back",
                "Join Ultimate Co-founder"
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"❌ Missing auth page elements: {missing_elements}")
                return False
            
            print("✅ Auth page functionality present")
            return True
            
        except Exception as e:
            print(f"❌ Auth page test error: {e}")
            return False
    
    async def test_footer_links(self) -> bool:
        """Test footer links"""
        try:
            print("🧪 Testing Footer Links...")
            
            response = await self.client.get(FRONTEND_URL)
            if response.status_code != 200:
                print(f"❌ Homepage not accessible for footer test: {response.status_code}")
                return False
            
            content = response.text
            
            # Check for footer links
            footer_links = [
                "Privacy Policy",
                "Terms of Service",
                "Security",
                "Contact",
                "Documentation",
                "API",
                "Community"
            ]
            
            missing_links = []
            for link in footer_links:
                if link not in content:
                    missing_links.append(link)
            
            if missing_links:
                print(f"❌ Missing footer links: {missing_links}")
                return False
            
            print("✅ Footer links present")
            return True
            
        except Exception as e:
            print(f"❌ Footer links test error: {e}")
            return False
    
    async def test_domain_references(self) -> bool:
        """Test that domain references use aicofounder.site"""
        try:
            print("🧪 Testing Domain References...")
            
            pages_to_check = ["/", "/auth", "/contact", "/about"]
            
            for page in pages_to_check:
                response = await self.client.get(f"{FRONTEND_URL}{page}")
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for old domain references
                    if "ultimate-cofounder.com" in content:
                        print(f"❌ Found old domain reference in {page}")
                        return False
                    
                    # Check for correct domain references
                    if "aicofounder.site" in content:
                        print(f"✅ Correct domain reference found in {page}")
            
            print("✅ Domain references correct")
            return True
            
        except Exception as e:
            print(f"❌ Domain references test error: {e}")
            return False
    
    async def test_api_endpoints_accessibility(self) -> bool:
        """Test that API endpoints are accessible"""
        try:
            print("🧪 Testing API Endpoints Accessibility...")
            
            # Test key API endpoints
            api_endpoints = [
                "/health",
                "/api/v1/health/",
                "/docs",
                "/openapi.json"
            ]
            
            backend_url = "http://localhost:8000"
            failed_endpoints = []
            
            for endpoint in api_endpoints:
                try:
                    response = await self.client.get(f"{backend_url}{endpoint}")
                    if response.status_code == 200:
                        print(f"✅ {endpoint} - OK")
                    else:
                        print(f"❌ {endpoint} - {response.status_code}")
                        failed_endpoints.append(endpoint)
                except Exception as e:
                    print(f"❌ {endpoint} - Error: {e}")
                    failed_endpoints.append(endpoint)
            
            if failed_endpoints:
                print(f"❌ Failed API endpoints: {failed_endpoints}")
                return False
            
            print("✅ All API endpoints accessible")
            return True
            
        except Exception as e:
            print(f"❌ API endpoints test error: {e}")
            return False
    
    async def run_comprehensive_navigation_test(self):
        """Run comprehensive navigation and link test"""
        print("🚀 Starting Comprehensive Navigation Test")
        print("=" * 60)
        
        tests = [
            ("Main Navigation Routes", self.test_main_navigation_routes),
            ("Homepage Content & CTAs", self.test_homepage_content),
            ("Auth Page Functionality", self.test_auth_page_functionality),
            ("Footer Links", self.test_footer_links),
            ("Domain References", self.test_domain_references),
            ("API Endpoints Accessibility", self.test_api_endpoints_accessibility),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    self.test_results.append({"test": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"test": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
                self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 NAVIGATION TEST SUMMARY")
        print("=" * 60)
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   Error: {result['error']}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL NAVIGATION TESTS PASSED!")
            print("🚀 All links and CTAs are working correctly!")
            return True
        else:
            print("⚠️  Some navigation tests FAILED!")
            return False
    
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main test runner"""
    tester = NavigationTester()
    try:
        success = await tester.run_comprehensive_navigation_test()
        return 0 if success else 1
    finally:
        await tester.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
