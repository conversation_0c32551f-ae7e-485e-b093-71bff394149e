# Frontend Environment Variables
VITE_API_URL=/api
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_LIVEKIT_URL=wss://your-livekit-server.livekit.cloud
VITE_LIVEKIT_API_KEY=your_livekit_api_key
VITE_LIVEKIT_SECRET_KEY=your_livekit_secret_key
VITE_COMPOSIO_API_KEY=your_composio_api_key
VITE_BOLT_API_KEY=your_bolt_api_key

# Note: When accessing the app via HTTPS (e.g., https://localhost), 
# the API service will automatically use HTTPS via the Nginx proxy.
# For HTTP development (http://localhost:5173), it will use the direct backend URL above.