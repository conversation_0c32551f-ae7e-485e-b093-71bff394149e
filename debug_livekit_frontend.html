<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit Frontend Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f8fff8; }
        .error { border-color: #f44336; background: #fff8f8; }
        .pending { border-color: #ff9800; background: #fff8f0; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.pending { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 LiveKit Frontend Debug Tool</h1>
        <p>This tool helps debug LiveKit integration issues in the Ultimate Co-founder application.</p>
        
        <div class="test-section pending" id="api-test">
            <h3>🔌 API Connectivity Test</h3>
            <p>Testing if frontend can reach the LiveKit API endpoints...</p>
            <button onclick="testAPIConnectivity()">Test API</button>
            <div class="log" id="api-log"></div>
        </div>
        
        <div class="test-section pending" id="session-test">
            <h3>🎬 Session Creation Test</h3>
            <p>Testing LiveKit session creation through the API...</p>
            <button onclick="testSessionCreation()">Test Session Creation</button>
            <div class="log" id="session-log"></div>
        </div>
        
        <div class="test-section pending" id="frontend-test">
            <h3>🌐 Frontend Integration Test</h3>
            <p>Testing frontend LiveKit service integration...</p>
            <button onclick="testFrontendIntegration()">Test Frontend</button>
            <div class="log" id="frontend-log"></div>
        </div>
        
        <div class="test-section pending" id="auth-test">
            <h3>🔐 Authentication Test</h3>
            <p>Testing authentication status and token validity...</p>
            <button onclick="testAuthentication()">Test Auth</button>
            <div class="log" id="auth-log"></div>
        </div>
        
        <div class="test-section pending" id="browser-test">
            <h3>🌍 Browser Environment Test</h3>
            <p>Testing browser capabilities and WebRTC support...</p>
            <button onclick="testBrowserEnvironment()">Test Browser</button>
            <div class="log" id="browser-log"></div>
        </div>
        
        <div class="test-section">
            <h3>🚀 Run All Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <script>
        function log(sectionId, message, type = 'info') {
            const logElement = document.getElementById(sectionId + '-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(sectionId, status) {
            const section = document.getElementById(sectionId);
            section.className = `test-section ${status}`;
        }
        
        async function testAPIConnectivity() {
            log('api', 'Testing API connectivity...');
            setStatus('api-test', 'pending');
            
            try {
                // Test backend API directly
                const backendResponse = await fetch('http://localhost:8000/api/v1/livekit/');
                if (backendResponse.ok) {
                    const data = await backendResponse.json();
                    log('api', `Backend API accessible: ${JSON.stringify(data)}`, 'success');
                } else {
                    log('api', `Backend API error: ${backendResponse.status}`, 'error');
                }
                
                // Test through Vite proxy
                const proxyResponse = await fetch('/api/v1/livekit/');
                if (proxyResponse.ok) {
                    const data = await proxyResponse.json();
                    log('api', `Vite proxy working: ${JSON.stringify(data)}`, 'success');
                    setStatus('api-test', 'success');
                } else {
                    log('api', `Vite proxy error: ${proxyResponse.status}`, 'error');
                    setStatus('api-test', 'error');
                }
            } catch (error) {
                log('api', `API test failed: ${error.message}`, 'error');
                setStatus('api-test', 'error');
            }
        }
        
        async function testSessionCreation() {
            log('session', 'Testing session creation...');
            setStatus('session-test', 'pending');
            
            try {
                const sessionData = {
                    agent_ids: ['strategic', 'product'],
                    session_type: 'video'
                };
                
                const response = await fetch('/api/v1/livekit/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(sessionData)
                });
                
                if (response.ok) {
                    const session = await response.json();
                    log('session', `Session created: ${session.id}`, 'success');
                    log('session', `Room: ${session.room_name}`, 'success');
                    log('session', `Participants: ${session.participants.length}`, 'success');
                    setStatus('session-test', 'success');
                } else {
                    const errorText = await response.text();
                    log('session', `Session creation failed: ${response.status} - ${errorText}`, 'error');
                    setStatus('session-test', 'error');
                }
            } catch (error) {
                log('session', `Session test failed: ${error.message}`, 'error');
                setStatus('session-test', 'error');
            }
        }
        
        async function testFrontendIntegration() {
            log('frontend', 'Testing frontend integration...');
            setStatus('frontend-test', 'pending');
            
            try {
                // Check if we can access the React app's window objects
                if (window.parent && window.parent !== window) {
                    log('frontend', 'Running in iframe, checking parent window...', 'info');
                    
                    // Try to access debugging objects set by our LiveKit service
                    if (window.parent.lastLiveKitSession) {
                        log('frontend', `Found session: ${window.parent.lastLiveKitSession.id}`, 'success');
                    }
                    
                    if (window.parent.lastLiveKitError) {
                        log('frontend', `Found error: ${window.parent.lastLiveKitError.message}`, 'error');
                    }
                }
                
                // Test if we can make requests to the same endpoints the React app uses
                const testResponse = await fetch('/api/v1/livekit/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_ids: ['strategic'],
                        session_type: 'video'
                    })
                });
                
                if (testResponse.ok) {
                    log('frontend', 'Frontend API integration working', 'success');
                    setStatus('frontend-test', 'success');
                } else {
                    log('frontend', `Frontend API error: ${testResponse.status}`, 'error');
                    setStatus('frontend-test', 'error');
                }
                
            } catch (error) {
                log('frontend', `Frontend test failed: ${error.message}`, 'error');
                setStatus('frontend-test', 'error');
            }
        }
        
        async function testAuthentication() {
            log('auth', 'Testing authentication...');
            setStatus('auth-test', 'pending');
            
            try {
                // Check if we can access auth endpoints
                const authResponse = await fetch('/api/v1/auth/me');
                
                if (authResponse.ok) {
                    const user = await authResponse.json();
                    log('auth', `User authenticated: ${user.email}`, 'success');
                    setStatus('auth-test', 'success');
                } else if (authResponse.status === 401) {
                    log('auth', 'User not authenticated (401)', 'error');
                    setStatus('auth-test', 'error');
                } else {
                    log('auth', `Auth check failed: ${authResponse.status}`, 'error');
                    setStatus('auth-test', 'error');
                }
            } catch (error) {
                log('auth', `Auth test failed: ${error.message}`, 'error');
                setStatus('auth-test', 'error');
            }
        }
        
        async function testBrowserEnvironment() {
            log('browser', 'Testing browser environment...');
            setStatus('browser-test', 'pending');
            
            try {
                // Check WebRTC support
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    log('browser', 'WebRTC getUserMedia supported', 'success');
                } else {
                    log('browser', 'WebRTC getUserMedia not supported', 'error');
                }
                
                // Check WebSocket support
                if (window.WebSocket) {
                    log('browser', 'WebSocket supported', 'success');
                } else {
                    log('browser', 'WebSocket not supported', 'error');
                }
                
                // Check if running on localhost
                if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                    log('browser', 'Running on localhost (good for development)', 'success');
                } else {
                    log('browser', `Running on ${location.hostname}`, 'info');
                }
                
                // Check HTTPS (required for some WebRTC features)
                if (location.protocol === 'https:' || location.hostname === 'localhost') {
                    log('browser', 'Secure context available', 'success');
                } else {
                    log('browser', 'Not in secure context (may affect WebRTC)', 'error');
                }
                
                setStatus('browser-test', 'success');
            } catch (error) {
                log('browser', `Browser test failed: ${error.message}`, 'error');
                setStatus('browser-test', 'error');
            }
        }
        
        async function runAllTests() {
            log('api', 'Starting comprehensive test suite...');
            await testAPIConnectivity();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testSessionCreation();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testFrontendIntegration();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testBrowserEnvironment();
        }
        
        function clearLogs() {
            const logs = document.querySelectorAll('.log');
            logs.forEach(log => log.innerHTML = '');
            
            const sections = document.querySelectorAll('.test-section');
            sections.forEach(section => {
                if (section.id) {
                    section.className = 'test-section pending';
                }
            });
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
