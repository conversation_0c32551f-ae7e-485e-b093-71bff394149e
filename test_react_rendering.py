#!/usr/bin/env python3
"""
React Rendering Test
Test if React app is properly rendering content
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any
import httpx
from datetime import datetime

# Test configuration
FRONTEND_URL = "http://localhost:5173"

class ReactRenderingTester:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def test_html_structure(self) -> bool:
        """Test basic HTML structure"""
        try:
            print("🧪 Testing HTML Structure...")
            
            response = await self.client.get(FRONTEND_URL)
            if response.status_code != 200:
                print(f"❌ Frontend not accessible: {response.status_code}")
                return False
            
            content = response.text
            
            # Check for basic HTML structure
            required_elements = [
                "<!doctype html>",
                "<html",
                "<head>",
                "<body>",
                '<div id="root">',
                '<script type="module" src="/src/main.tsx'
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"❌ Missing HTML elements: {missing_elements}")
                return False
            
            print("✅ HTML structure is correct")
            return True
            
        except Exception as e:
            print(f"❌ HTML structure test error: {e}")
            return False
    
    async def test_vite_assets(self) -> bool:
        """Test if Vite assets are loading"""
        try:
            print("🧪 Testing Vite Assets...")
            
            # Test main.tsx
            response = await self.client.get(f"{FRONTEND_URL}/src/main.tsx")
            if response.status_code != 200:
                print(f"❌ main.tsx not accessible: {response.status_code}")
                return False
            
            main_content = response.text
            if "createRoot" not in main_content:
                print("❌ main.tsx doesn't contain React createRoot")
                return False
            
            print("✅ main.tsx is accessible and contains React code")
            
            # Test App.tsx
            response = await self.client.get(f"{FRONTEND_URL}/src/App.tsx")
            if response.status_code != 200:
                print(f"❌ App.tsx not accessible: {response.status_code}")
                return False
            
            app_content = response.text
            if "function App" not in app_content and "const App" not in app_content:
                print("❌ App.tsx doesn't contain App component")
                return False
            
            print("✅ App.tsx is accessible and contains App component")
            
            # Test index.css
            response = await self.client.get(f"{FRONTEND_URL}/src/index.css")
            if response.status_code != 200:
                print(f"❌ index.css not accessible: {response.status_code}")
                return False
            
            print("✅ index.css is accessible")
            
            return True
            
        except Exception as e:
            print(f"❌ Vite assets test error: {e}")
            return False
    
    async def test_javascript_execution(self) -> bool:
        """Test if JavaScript is executing by checking for dynamic content"""
        try:
            print("🧪 Testing JavaScript Execution...")
            
            # Wait a moment for potential JS execution
            await asyncio.sleep(2)
            
            response = await self.client.get(FRONTEND_URL)
            content = response.text
            
            # Check if the content is just the static HTML or if React has rendered
            if '<div id="root"></div>' in content:
                print("❌ React app not rendering - root div is empty")
                print("This suggests JavaScript/React is not executing properly")
                return False
            
            # Check for any signs of React rendering
            react_indicators = [
                "data-reactroot",
                "react-",
                "__REACT_DEVTOOLS_GLOBAL_HOOK__"
            ]
            
            has_react_indicators = any(indicator in content for indicator in react_indicators)
            
            if not has_react_indicators:
                print("⚠️  No React indicators found in rendered content")
                print("This might indicate React is not rendering properly")
            else:
                print("✅ React indicators found in content")
            
            return True
            
        except Exception as e:
            print(f"❌ JavaScript execution test error: {e}")
            return False
    
    async def test_api_connectivity(self) -> bool:
        """Test if frontend can connect to backend API"""
        try:
            print("🧪 Testing API Connectivity...")
            
            # Test if frontend can proxy to backend
            response = await self.client.get(f"{FRONTEND_URL}/api/v1/health")
            if response.status_code == 200:
                print("✅ Frontend can proxy to backend API")
                return True
            else:
                print(f"❌ Frontend API proxy failed: {response.status_code}")
                
                # Test direct backend connection
                backend_response = await self.client.get("http://localhost:8000/api/v1/health")
                if backend_response.status_code == 200:
                    print("✅ Backend is accessible directly")
                    print("❌ Issue is with frontend proxy configuration")
                else:
                    print("❌ Backend is not accessible")
                
                return False
            
        except Exception as e:
            print(f"❌ API connectivity test error: {e}")
            return False
    
    async def run_comprehensive_test(self):
        """Run comprehensive React rendering test"""
        print("🚀 Starting React Rendering Diagnostic Test")
        print("=" * 60)
        
        tests = [
            ("HTML Structure", self.test_html_structure),
            ("Vite Assets Loading", self.test_vite_assets),
            ("JavaScript Execution", self.test_javascript_execution),
            ("API Connectivity", self.test_api_connectivity),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Testing: {test_name}")
            try:
                if await test_func():
                    passed += 1
                    print(f"✅ {test_name}: PASS")
                else:
                    print(f"❌ {test_name}: FAIL")
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 REACT RENDERING DIAGNOSTIC SUMMARY")
        print("=" * 60)
        
        print(f"📈 Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL REACT RENDERING TESTS PASSED!")
            print("🚀 React app should be rendering correctly!")
        else:
            print("⚠️  React rendering issues detected!")
            print("\n🔧 TROUBLESHOOTING SUGGESTIONS:")
            print("1. Check browser console for JavaScript errors")
            print("2. Verify all dependencies are installed (npm install)")
            print("3. Check if Vite dev server is running properly")
            print("4. Clear browser cache and reload")
            print("5. Check for TypeScript compilation errors")
        
        return passed == total
    
    async def close(self):
        """Clean up resources"""
        await self.client.aclose()

async def main():
    """Main test runner"""
    tester = ReactRenderingTester()
    try:
        success = await tester.run_comprehensive_test()
        return 0 if success else 1
    finally:
        await tester.close()

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
