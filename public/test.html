<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-box {
            background-color: #4CAF50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>✅ Basic HTML/CSS Test</h1>
        <p>If you can see this green box, basic HTML/CSS is working.</p>
    </div>
    
    <div id="js-test" style="background-color: red; color: white; padding: 20px; border-radius: 5px;">
        ❌ JavaScript NOT working
    </div>
    
    <script>
        console.log('JavaScript is executing');
        document.getElementById('js-test').innerHTML = '<h2>✅ JavaScript Test</h2><p>JavaScript is working correctly!</p>';
        document.getElementById('js-test').style.backgroundColor = '#2196F3';
    </script>
</body>
</html>
