<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Ultimate Co-founder</title>
    <style>
        body {
            font-family: <PERSON>l, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .debug-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            backdrop-filter: blur(10px);
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="debug-box info">
        <h1>🔧 Ultimate Co-founder Debug Page</h1>
        <p>This page helps diagnose loading issues</p>
    </div>

    <div class="debug-box">
        <h2>🌐 Network Tests</h2>
        <button onclick="testBackendHealth()">Test Backend Health</button>
        <button onclick="testFrontendAssets()">Test Frontend Assets</button>
        <button onclick="testAPIConnection()">Test API Connection</button>
    </div>

    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(title, status, message, isError = false) {
            const div = document.createElement('div');
            div.className = `debug-box ${isError ? 'error' : 'success'}`;
            div.innerHTML = `
                <h3>${status} ${title}</h3>
                <p>${message}</p>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            results.appendChild(div);
        }

        async function testBackendHealth() {
            try {
                console.log('Testing backend health...');
                const response = await fetch('http://127.0.0.1:8000/health');
                const data = await response.json();
                addResult('Backend Health', '✅', `Backend is healthy: ${JSON.stringify(data)}`);
            } catch (error) {
                console.error('Backend health test failed:', error);
                addResult('Backend Health', '❌', `Backend health check failed: ${error.message}`, true);
            }
        }

        async function testFrontendAssets() {
            try {
                console.log('Testing frontend assets...');
                const response = await fetch('/src/main.tsx');
                if (response.ok) {
                    addResult('Frontend Assets', '✅', 'main.tsx loads successfully');
                } else {
                    addResult('Frontend Assets', '❌', `main.tsx failed to load: ${response.status}`, true);
                }
            } catch (error) {
                console.error('Frontend assets test failed:', error);
                addResult('Frontend Assets', '❌', `Frontend assets test failed: ${error.message}`, true);
            }
        }

        async function testAPIConnection() {
            try {
                console.log('Testing API connection...');
                const response = await fetch('http://127.0.0.1:8000/api/v1/agents/');
                const data = await response.json();
                addResult('API Connection', '✅', `API working: Found ${data.agents ? data.agents.length : 0} agents`);
            } catch (error) {
                console.error('API connection test failed:', error);
                addResult('API Connection', '❌', `API connection failed: ${error.message}`, true);
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('Page Load', '✅', 'Debug page loaded successfully');
            setTimeout(() => {
                testBackendHealth();
                testFrontendAssets();
                testAPIConnection();
            }, 1000);
        });

        // Log browser info
        console.log('Browser Info:', {
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>
