#!/usr/bin/env python3
"""
TopView.ai Integration Test Suite

Comprehensive testing of TopView.ai video creation functionality
including backend API, frontend integration, and end-to-end workflows.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any
import sys
import os

# Add backend to path for imports
sys.path.append('backend')

class TopViewIntegrationTest:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5173"
        self.session = None
        self.auth_token = None
        self.test_results = []
        
    async def setup(self):
        """Setup test environment"""
        print("🔧 Setting up TopView.ai Integration Test...")
        
        # Create aiohttp session
        self.session = aiohttp.ClientSession()
        
        # Authenticate to get token
        await self.authenticate()
        
    async def cleanup(self):
        """Cleanup test environment"""
        if self.session:
            await self.session.close()
            
    async def authenticate(self):
        """Authenticate with demo user"""
        print("🔐 Authenticating with demo user...")
        
        try:
            data = aiohttp.FormData()
            data.add_field('username', '<EMAIL>')
            data.add_field('password', 'demo123')
            
            async with self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    self.auth_token = result.get('access_token')
                    print("✅ Authentication successful")
                    return True
                else:
                    print(f"❌ Authentication failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def get_auth_headers(self):
        """Get authorization headers"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    async def test_topview_health(self):
        """Test TopView.ai service health endpoint"""
        print("\n🏥 Testing TopView.ai Health Status...")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/v1/topview/health",
                headers=self.get_auth_headers()
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ TopView.ai Health: {result.get('status')}")
                    print(f"   Mock Mode: {result.get('mock_mode', False)}")
                    print(f"   API Key Configured: {result.get('api_key_configured', False)}")
                    print(f"   Features: {len(result.get('features', []))}")
                    
                    self.test_results.append({
                        "test": "topview_health",
                        "status": "PASS",
                        "details": result
                    })
                    return True
                else:
                    print(f"❌ Health check failed: {response.status}")
                    self.test_results.append({
                        "test": "topview_health",
                        "status": "FAIL",
                        "error": f"HTTP {response.status}"
                    })
                    return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            self.test_results.append({
                "test": "topview_health",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_get_voices_and_avatars(self):
        """Test getting available voices and avatars"""
        print("\n🎤 Testing Voices and Avatars...")
        
        try:
            # Test voices
            async with self.session.get(
                f"{self.base_url}/api/v1/topview/voices",
                headers=self.get_auth_headers()
            ) as response:
                if response.status == 200:
                    voices = await response.json()
                    print(f"✅ Voices available: {len(voices.get('voices', []))}")
                else:
                    print(f"❌ Voices request failed: {response.status}")
                    return False
            
            # Test avatars
            async with self.session.get(
                f"{self.base_url}/api/v1/topview/avatars",
                headers=self.get_auth_headers()
            ) as response:
                if response.status == 200:
                    avatars = await response.json()
                    print(f"✅ Avatars available: {len(avatars.get('avatars', []))}")
                    
                    self.test_results.append({
                        "test": "voices_avatars",
                        "status": "PASS",
                        "details": {
                            "voices": len(voices.get('voices', [])),
                            "avatars": len(avatars.get('avatars', []))
                        }
                    })
                    return True
                else:
                    print(f"❌ Avatars request failed: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ Voices/Avatars error: {e}")
            self.test_results.append({
                "test": "voices_avatars",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_url_to_video(self):
        """Test URL-to-Video creation"""
        print("\n🔗 Testing URL-to-Video Creation...")
        
        try:
            data = {
                "url": "https://aicofounder.site",
                "duration": 30,
                "aspectRatio": "16:9",
                "voiceId": "default",
                "avatarId": "default",
                "title": "AI Co-founder Platform Demo"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/topview/url-to-video",
                headers={**self.get_auth_headers(), "Content-Type": "application/json"},
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('taskId')
                    print(f"✅ URL-to-Video task created: {task_id}")
                    print(f"   Status: {result.get('status')}")
                    print(f"   Type: {result.get('videoType')}")
                    
                    # Test task query
                    await asyncio.sleep(2)  # Wait a bit
                    query_result = await self.test_task_query(task_id)
                    
                    self.test_results.append({
                        "test": "url_to_video",
                        "status": "PASS",
                        "details": {
                            "task_id": task_id,
                            "creation_status": result.get('status'),
                            "query_result": query_result
                        }
                    })
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ URL-to-Video failed: {response.status} - {error_text}")
                    self.test_results.append({
                        "test": "url_to_video",
                        "status": "FAIL",
                        "error": f"HTTP {response.status}: {error_text}"
                    })
                    return False
                    
        except Exception as e:
            print(f"❌ URL-to-Video error: {e}")
            self.test_results.append({
                "test": "url_to_video",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_video_avatar(self):
        """Test Video Avatar creation"""
        print("\n🎭 Testing Video Avatar Creation...")
        
        try:
            data = {
                "script": "Welcome to AI Co-founder! We help entrepreneurs build successful startups with our team of 5 specialized AI agents. Let's transform your idea into reality!",
                "avatarId": "default",
                "voiceId": "default",
                "backgroundType": "solid",
                "backgroundColor": "#ffffff"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/topview/video-avatar",
                headers={**self.get_auth_headers(), "Content-Type": "application/json"},
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('taskId')
                    print(f"✅ Video Avatar task created: {task_id}")
                    print(f"   Status: {result.get('status')}")
                    print(f"   Script length: {len(data['script'])} characters")
                    
                    self.test_results.append({
                        "test": "video_avatar",
                        "status": "PASS",
                        "details": {
                            "task_id": task_id,
                            "status": result.get('status'),
                            "script_length": len(data['script'])
                        }
                    })
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Video Avatar failed: {response.status} - {error_text}")
                    self.test_results.append({
                        "test": "video_avatar",
                        "status": "FAIL",
                        "error": f"HTTP {response.status}: {error_text}"
                    })
                    return False
                    
        except Exception as e:
            print(f"❌ Video Avatar error: {e}")
            self.test_results.append({
                "test": "video_avatar",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_image_to_video(self):
        """Test Image-to-Video creation"""
        print("\n🖼️ Testing Image-to-Video Creation...")
        
        try:
            data = {
                "imageUrl": "https://images.unsplash.com/photo-1551434678-e076c223a692?w=800",
                "duration": 5,
                "motionStrength": "medium"
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/topview/image-to-video",
                headers={**self.get_auth_headers(), "Content-Type": "application/json"},
                json=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('taskId')
                    print(f"✅ Image-to-Video task created: {task_id}")
                    print(f"   Status: {result.get('status')}")
                    print(f"   Duration: {data['duration']}s")
                    
                    self.test_results.append({
                        "test": "image_to_video",
                        "status": "PASS",
                        "details": {
                            "task_id": task_id,
                            "status": result.get('status'),
                            "duration": data['duration']
                        }
                    })
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Image-to-Video failed: {response.status} - {error_text}")
                    self.test_results.append({
                        "test": "image_to_video",
                        "status": "FAIL",
                        "error": f"HTTP {response.status}: {error_text}"
                    })
                    return False
                    
        except Exception as e:
            print(f"❌ Image-to-Video error: {e}")
            self.test_results.append({
                "test": "image_to_video",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def test_task_query(self, task_id: str):
        """Test task status query"""
        print(f"🔍 Querying task status: {task_id}")
        
        try:
            async with self.session.get(
                f"{self.base_url}/api/v1/topview/task/{task_id}",
                headers=self.get_auth_headers()
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Task query successful: {result.get('status')}")
                    if result.get('videoUrl'):
                        print(f"   Video URL: {result.get('videoUrl')}")
                    if result.get('videoDuration'):
                        print(f"   Duration: {result.get('videoDuration')}ms")
                    return result
                else:
                    print(f"❌ Task query failed: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ Task query error: {e}")
            return None
    
    async def test_frontend_integration(self):
        """Test frontend TopView.ai page accessibility"""
        print("\n🌐 Testing Frontend Integration...")
        
        try:
            async with self.session.get(f"{self.frontend_url}/topview") as response:
                if response.status == 200:
                    content = await response.text()
                    if "TopView.ai" in content:
                        print("✅ Frontend TopView.ai page accessible")
                        self.test_results.append({
                            "test": "frontend_integration",
                            "status": "PASS",
                            "details": "TopView.ai page accessible"
                        })
                        return True
                    else:
                        print("❌ TopView.ai content not found on page")
                        return False
                else:
                    print(f"❌ Frontend page not accessible: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Frontend integration error: {e}")
            self.test_results.append({
                "test": "frontend_integration",
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    async def run_all_tests(self):
        """Run all TopView.ai integration tests"""
        print("🎬 Starting TopView.ai Integration Test Suite")
        print("=" * 60)
        
        await self.setup()
        
        tests = [
            self.test_topview_health,
            self.test_get_voices_and_avatars,
            self.test_url_to_video,
            self.test_video_avatar,
            self.test_image_to_video,
            self.test_frontend_integration
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ Test {test.__name__} crashed: {e}")
                failed += 1
        
        await self.cleanup()
        
        # Print summary
        print("\n" + "=" * 60)
        print("🎬 TopView.ai Integration Test Summary")
        print("=" * 60)
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Success Rate: {(passed / (passed + failed) * 100):.1f}%")
        
        # Print detailed results
        print("\n📋 Detailed Results:")
        for result in self.test_results:
            status_emoji = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
            print(f"{status_emoji} {result['test']}: {result['status']}")
            if result.get('error'):
                print(f"   Error: {result['error']}")
            if result.get('details'):
                print(f"   Details: {result['details']}")
        
        return passed == len(tests)

async def main():
    """Main test runner"""
    tester = TopViewIntegrationTest()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 All TopView.ai integration tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some TopView.ai integration tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
