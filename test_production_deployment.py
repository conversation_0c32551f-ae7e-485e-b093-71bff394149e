#!/usr/bin/env python3
"""
Ultimate Co-founder Production Deployment Test Suite
Comprehensive testing of all application components and integrations
"""

import requests
import json
import time
import subprocess
import os
import sys
from datetime import datetime
from typing import Dict, List, Any

class ProductionTestSuite:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5173"
        self.test_results = []
        self.start_time = datetime.now()
        
    def log_test(self, test_name: str, status: str, details: str = "", duration: float = 0):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "duration": f"{duration:.2f}s",
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        # Print real-time results
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   └─ {details}")
    
    def test_backend_health(self):
        """Test backend server health and availability"""
        start = time.time()
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_test("Backend Health Check", "PASS", 
                            f"Status: {data.get('status', 'unknown')}", time.time() - start)
                return True
            else:
                self.log_test("Backend Health Check", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("Backend Health Check", "FAIL", str(e), time.time() - start)
            return False
    
    def test_api_documentation(self):
        """Test API documentation accessibility"""
        start = time.time()
        try:
            response = requests.get(f"{self.backend_url}/docs", timeout=10)
            if response.status_code == 200:
                self.log_test("API Documentation", "PASS", 
                            "Swagger UI accessible", time.time() - start)
                return True
            else:
                self.log_test("API Documentation", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("API Documentation", "FAIL", str(e), time.time() - start)
            return False
    
    def test_ai_agents_endpoint(self):
        """Test AI agents listing endpoint"""
        start = time.time()
        try:
            response = requests.get(f"{self.backend_url}/api/v1/agents/", timeout=10)
            if response.status_code == 200:
                agents = response.json()
                agent_count = len(agents)
                self.log_test("AI Agents Endpoint", "PASS", 
                            f"Found {agent_count} agents", time.time() - start)
                return True
            else:
                self.log_test("AI Agents Endpoint", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("AI Agents Endpoint", "FAIL", str(e), time.time() - start)
            return False
    
    def test_ai_agent_execution(self):
        """Test AI agent execution with real API"""
        start = time.time()
        try:
            payload = {
                "description": "Create a brief business plan for a SaaS startup",
                "context": "Testing production deployment"
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/agents/execute?agent_id=strategic",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                result = response.json()
                self.log_test("AI Agent Execution", "PASS", 
                            f"Strategic agent responded with {len(result.get('result', ''))} chars", 
                            time.time() - start)
                return True
            else:
                self.log_test("AI Agent Execution", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("AI Agent Execution", "FAIL", str(e), time.time() - start)
            return False
    
    def test_multi_agent_execution(self):
        """Test multi-agent collaboration"""
        start = time.time()
        try:
            payload = {
                "description": "Create a startup strategy for a fintech app",
                "agent_ids": ["strategic", "product"]
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/agents/execute-multi",
                json=payload,
                timeout=45
            )
            if response.status_code == 200:
                result = response.json()
                self.log_test("Multi-Agent Execution", "PASS", 
                            f"Collaboration completed with {len(result.get('results', []))} agents", 
                            time.time() - start)
                return True
            else:
                self.log_test("Multi-Agent Execution", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("Multi-Agent Execution", "FAIL", str(e), time.time() - start)
            return False
    
    def test_skywork_integration(self):
        """Test SkyworkAI integration"""
        start = time.time()
        try:
            payload = {
                "type": "business_plan",
                "title": "SaaS Startup Business Plan",
                "prompt": "Create a comprehensive business plan for a SaaS startup"
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/skywork/generate",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                result = response.json()
                self.log_test("SkyworkAI Integration", "PASS", 
                            f"Document generated: {result.get('title', 'Unknown')}", 
                            time.time() - start)
                return True
            else:
                self.log_test("SkyworkAI Integration", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("SkyworkAI Integration", "FAIL", str(e), time.time() - start)
            return False
    
    def test_topview_integration(self):
        """Test TopView.ai integration"""
        start = time.time()
        try:
            payload = {
                "url": "https://example.com",
                "duration": 30,
                "aspectRatio": "16:9"
            }
            response = requests.post(
                f"{self.backend_url}/api/v1/topview/url-to-video",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                result = response.json()
                self.log_test("TopView.ai Integration", "PASS", 
                            f"Video task created: {result.get('task_id', 'Unknown')}", 
                            time.time() - start)
                return True
            else:
                self.log_test("TopView.ai Integration", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("TopView.ai Integration", "FAIL", str(e), time.time() - start)
            return False
    
    def test_frontend_build(self):
        """Test frontend production build"""
        start = time.time()
        try:
            # Check if dist directory exists
            if os.path.exists("dist"):
                # Check for key files
                required_files = ["dist/index.html", "dist/assets"]
                missing_files = [f for f in required_files if not os.path.exists(f)]
                
                if not missing_files:
                    self.log_test("Frontend Production Build", "PASS", 
                                "All required build files present", time.time() - start)
                    return True
                else:
                    self.log_test("Frontend Production Build", "FAIL", 
                                f"Missing files: {missing_files}", time.time() - start)
                    return False
            else:
                self.log_test("Frontend Production Build", "FAIL", 
                            "dist directory not found", time.time() - start)
                return False
        except Exception as e:
            self.log_test("Frontend Production Build", "FAIL", str(e), time.time() - start)
            return False
    
    def test_frontend_accessibility(self):
        """Test frontend accessibility"""
        start = time.time()
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                html_content = response.text
                # Check for essential HTML elements
                if 'id="root"' in html_content and '<script' in html_content:
                    self.log_test("Frontend Accessibility", "PASS", 
                                "HTML structure valid", time.time() - start)
                    return True
                else:
                    self.log_test("Frontend Accessibility", "FAIL", 
                                "Invalid HTML structure", time.time() - start)
                    return False
            else:
                self.log_test("Frontend Accessibility", "FAIL", 
                            f"HTTP {response.status_code}", time.time() - start)
                return False
        except Exception as e:
            self.log_test("Frontend Accessibility", "FAIL", str(e), time.time() - start)
            return False
    
    def run_all_tests(self):
        """Run complete test suite"""
        print("🚀 Starting Ultimate Co-founder Production Test Suite")
        print("=" * 60)
        
        # Backend Tests
        print("\n📡 BACKEND TESTS")
        print("-" * 30)
        self.test_backend_health()
        self.test_api_documentation()
        self.test_ai_agents_endpoint()
        self.test_ai_agent_execution()
        self.test_multi_agent_execution()
        self.test_skywork_integration()
        self.test_topview_integration()
        
        # Frontend Tests
        print("\n🎨 FRONTEND TESTS")
        print("-" * 30)
        self.test_frontend_build()
        self.test_frontend_accessibility()
        
        # Generate Report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report"""
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        passed_tests = [t for t in self.test_results if t["status"] == "PASS"]
        failed_tests = [t for t in self.test_results if t["status"] == "FAIL"]
        
        print("\n" + "=" * 60)
        print("📊 PRODUCTION DEPLOYMENT TEST REPORT")
        print("=" * 60)
        
        print(f"\n⏱️  Total Test Duration: {total_duration:.2f} seconds")
        print(f"✅ Passed Tests: {len(passed_tests)}/{len(self.test_results)}")
        print(f"❌ Failed Tests: {len(failed_tests)}/{len(self.test_results)}")
        print(f"📈 Success Rate: {(len(passed_tests)/len(self.test_results)*100):.1f}%")
        
        if failed_tests:
            print(f"\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"   • {test['test']}: {test['details']}")
        
        print(f"\n🎉 PRODUCTION STATUS: {'READY' if len(failed_tests) == 0 else 'NEEDS ATTENTION'}")
        
        # Save detailed report
        report_data = {
            "summary": {
                "total_tests": len(self.test_results),
                "passed": len(passed_tests),
                "failed": len(failed_tests),
                "success_rate": len(passed_tests)/len(self.test_results)*100,
                "duration": total_duration,
                "timestamp": end_time.isoformat()
            },
            "tests": self.test_results
        }
        
        with open("production_test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: production_test_report.json")

if __name__ == "__main__":
    suite = ProductionTestSuite()
    suite.run_all_tests()
